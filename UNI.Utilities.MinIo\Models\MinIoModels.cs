using System;
using System.Collections.Generic;

namespace UNI.Utilities.MinIo
{
    /// <summary>
    /// Information about a bucket
    /// </summary>
    public class BucketInfo
    {
        /// <summary>
        /// Name of the bucket
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// Creation date of the bucket
        /// </summary>
        public DateTime CreationDate { get; set; }

        /// <summary>
        /// Owner of the bucket
        /// </summary>
        public string? Owner { get; set; }
    }

    /// <summary>
    /// Information about an object
    /// </summary>
    public class ObjectInfo
    {
        /// <summary>
        /// Name of the object
        /// </summary>
        public string ObjectName { get; set; } = string.Empty;

        /// <summary>
        /// Name of the bucket containing the object
        /// </summary>
        public string BucketName { get; set; } = string.Empty;

        /// <summary>
        /// Size of the object in bytes
        /// </summary>
        public long Size { get; set; }

        /// <summary>
        /// Last modified date of the object
        /// </summary>
        public DateTime LastModified { get; set; }

        /// <summary>
        /// ETag of the object
        /// </summary>
        public string? ETag { get; set; }

        /// <summary>
        /// Content type of the object
        /// </summary>
        public string? ContentType { get; set; }

        /// <summary>
        /// Metadata associated with the object
        /// </summary>
        public Dictionary<string, string> Metadata { get; set; } = new Dictionary<string, string>();

        /// <summary>
        /// Storage class of the object
        /// </summary>
        public string? StorageClass { get; set; }

        /// <summary>
        /// Whether the object is a directory
        /// </summary>
        public bool IsDir { get; set; }
    }

    /// <summary>
    /// Result of an upload operation
    /// </summary>
    public class UploadResult
    {
        /// <summary>
        /// Name of the bucket
        /// </summary>
        public string BucketName { get; set; } = string.Empty;

        /// <summary>
        /// Name of the uploaded object
        /// </summary>
        public string ObjectName { get; set; } = string.Empty;

        /// <summary>
        /// ETag of the uploaded object
        /// </summary>
        public string? ETag { get; set; }

        /// <summary>
        /// Size of the uploaded object in bytes
        /// </summary>
        public long Size { get; set; }

        /// <summary>
        /// Content type of the uploaded object
        /// </summary>
        public string? ContentType { get; set; }

        /// <summary>
        /// Upload completion time
        /// </summary>
        public DateTime UploadTime { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// Version ID of the uploaded object (if versioning is enabled)
        /// </summary>
        public string? VersionId { get; set; }

        /// <summary>
        /// Whether this was a multipart upload
        /// </summary>
        public bool IsMultipartUpload { get; set; }

        /// <summary>
        /// Number of parts uploaded (for multipart uploads)
        /// </summary>
        public int? PartCount { get; set; }
    }

    /// <summary>
    /// Progress information for upload operations
    /// </summary>
    public class UploadProgress
    {
        /// <summary>
        /// Total bytes to upload
        /// </summary>
        public long TotalBytes { get; set; }

        /// <summary>
        /// Bytes uploaded so far
        /// </summary>
        public long UploadedBytes { get; set; }

        /// <summary>
        /// Upload progress as percentage (0-100)
        /// </summary>
        public double PercentageComplete => TotalBytes > 0 ? (double)UploadedBytes / TotalBytes * 100 : 0;

        /// <summary>
        /// Current part being uploaded (for multipart uploads)
        /// </summary>
        public int? CurrentPart { get; set; }

        /// <summary>
        /// Total number of parts (for multipart uploads)
        /// </summary>
        public int? TotalParts { get; set; }

        /// <summary>
        /// Upload speed in bytes per second
        /// </summary>
        public long? BytesPerSecond { get; set; }

        /// <summary>
        /// Estimated time remaining
        /// </summary>
        public TimeSpan? EstimatedTimeRemaining { get; set; }

        /// <summary>
        /// Whether this is a multipart upload
        /// </summary>
        public bool IsMultipartUpload { get; set; }

        /// <summary>
        /// Current operation status
        /// </summary>
        public string Status { get; set; } = "Uploading";
    }

    /// <summary>
    /// Result of a delete operation
    /// </summary>
    public class DeleteResult
    {
        /// <summary>
        /// Name of the object that was deleted
        /// </summary>
        public string ObjectName { get; set; } = string.Empty;

        /// <summary>
        /// Whether the deletion was successful
        /// </summary>
        public bool IsSuccess { get; set; }

        /// <summary>
        /// Error message if deletion failed
        /// </summary>
        public string? ErrorMessage { get; set; }

        /// <summary>
        /// Version ID of the deleted object (if versioning is enabled)
        /// </summary>
        public string? VersionId { get; set; }
    }

    /// <summary>
    /// Configuration settings for MinIO client
    /// </summary>
    public class MinIoSettings
    {
        /// <summary>
        /// MinIO server endpoint (internal/direct endpoint)
        /// </summary>
        public string Endpoint { get; set; } = string.Empty;

        /// <summary>
        /// Proxy endpoint for public access (optional)
        /// This is used for generating presigned URLs that are accessible from outside
        /// </summary>
        public string? ProxyEndpoint { get; set; }

        /// <summary>
        /// Access key for authentication
        /// </summary>
        public string AccessKey { get; set; } = string.Empty;

        /// <summary>
        /// Secret key for authentication
        /// </summary>
        public string SecretKey { get; set; } = string.Empty;

        /// <summary>
        /// Whether to use SSL/TLS
        /// </summary>
        public bool UseSSL { get; set; } = true;

        /// <summary>
        /// Region for the MinIO server (optional)
        /// </summary>
        public string? Region { get; set; }

        /// <summary>
        /// Default bucket name to use if not specified in operations
        /// </summary>
        public string? DefaultBucket { get; set; }

        /// <summary>
        /// Connection timeout in seconds
        /// </summary>
        public int TimeoutSeconds { get; set; } = 30;

        /// <summary>
        /// Whether to create bucket if it doesn't exist
        /// </summary>
        public bool CreateBucketIfNotExists { get; set; } = false;
    }

    /// <summary>
    /// Exception thrown by MinIO operations
    /// </summary>
    public class MinIoException : Exception
    {
        /// <summary>
        /// MinIO error code
        /// </summary>
        public string? ErrorCode { get; set; }

        /// <summary>
        /// Bucket name related to the error
        /// </summary>
        public string? BucketName { get; set; }

        /// <summary>
        /// Object name related to the error
        /// </summary>
        public string? ObjectName { get; set; }

        public MinIoException() : base() { }

        public MinIoException(string message) : base(message) { }

        public MinIoException(string message, Exception innerException) : base(message, innerException) { }

        public MinIoException(string message, string? errorCode, string? bucketName = null, string? objectName = null)
            : base(message)
        {
            ErrorCode = errorCode;
            BucketName = bucketName;
            ObjectName = objectName;
        }
    }
}
