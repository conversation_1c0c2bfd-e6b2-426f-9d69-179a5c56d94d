﻿# default language English
1.2.does.not.contain.an.usable.cmap={1} {2} does not contain an usable cmap.
1.2.is.not.a.ttf.font.file={1} {2} is not a TTF font file.
1.at.file.pointer.2={1} at file pointer {2}
1.bit.samples.are.not.supported.for.horizontal.differencing.predictor={1}-bit samples are not supported for Horizontal differencing Predictor.
1.cannot.be.embedded.due.to.licensing.restrictions={1} cannot be embedded due to licensing restrictions.
1.component.s.is.not.supported={1} component(s) is not supported
1.corrupted.jfif.marker={1} corrupted JFIF marker.
1.is.an.unknown.graphics.state.dictionary={1} is an unknown graphics state dictionary
1.is.an.unknown.image.format={1} is an unknown Image format.
1.is.not.a.true.type.file={1} is not a true type file.
1.is.not.a.ttf.otf.or.ttc.font.file={1} is not a TTF, OTF or TTC font file.
1.is.not.a.valid.jpeg.file={1} is not a valid JPEG-file.
1.is.not.a.valid.name.for.checkbox.appearance={1} is not a valid name for a checkbox appearance (should be Off or Yes)
1.is.not.a.valid.number.2={1} is not a valid number - {2}
1.is.not.a.valid.page.size.format.2={1} is not a valid page size format: {2}
1.is.not.a.valid.placeable.windows.metafile={1} is not a valid placeable windows metafile.
1.is.not.a.valid.ttc.file={1} is not a valid TTC file.
1.is.not.a.valid.ttf.file={1} is not a valid TTF file.
1.is.not.a.valid.ttf.or.otf.file={1} is not a valid TTF or OTF file.
1.is.not.an.acceptable.value.for.the.field.2={1} is not an acceptable value for the field {2}
1.is.not.an.afm.or.pfm.font.file={1} is not an AFM or PFM font file.
1.method.can.be.only.used.in.mergeFields.mode.please.use.addDocument={1} method can be only used in mergeFields mode, please use addDocument.
1.method.cannot.be.used.in.mergeFields.mode.please.use.addDocument={1} method cannot be used in mergeFields mode, please use addDocument.
1.must.have.8.bits.per.component={1} must have 8 bits per component.
1.near.line.2.column.3={1} near line {2}, column {3}
1.not.found.as.file.or.resource={1} not found as file or resource.
1.not.found.as.resource={1} not found as resource.
1.unsupported.jpeg.marker.2={1}: unsupported JPEG marker: {2}
1.value.of.intent.key.is.not.allowed={1} value of Intent key is not allowed.
1.value.of.ri.key.is.not.allowed={1} value of RI key is not allowed.
a.form.xobject.dictionary.shall.not.contain.ps.key=A form XObject dictionary shall not contain PS key.
a.form.xobject.dictionary.shall.not.contain.opi.key=A form XObject dictionary shall not contain OPI key.
a.group.object.with.an.s.key.with.a.value.of.transparency.shall.not.be.included.in.a.form.xobject=A Group object with an S key with a value of Transparency shall not be included in a form XObject.
a.pattern.can.not.be.used.as.a.template.to.create.an.image=A pattern can not be used as a template to create an image.
a.pdfa.file.may.have.only.one.pdfa.outputintent=A PDF/A conforming file may have only one PDF/A OutputIntent.
a.pdfx.conforming.document.cannot.be.encrypted=A PDFX conforming document cannot be encrypted.
a.signature.image.should.be.present.when.rendering.mode.is.graphic.only=A signature image should be present when rendering mode is graphic only.
a.signature.image.should.be.present.when.rendering.mode.is.graphic.and.description=A signature image should be present when rendering mode is graphic and description.
a.string.1.was.passed.in.state.only.on.off.and.toggle.are.allowed=A string '{1} was passed in state. Only 'ON', 'OFF' and 'Toggle' are allowed.
a.tab.position.may.not.be.lower.than.0.yours.is.1=A tab position may not be lower than 0; yours is {1}
a.table.should.have.at.least.1.column=A table should have at least 1 column.
a.tiling.or.shading.pattern.cannot.be.used.as.a.color.space.in.a.shading.pattern=A tiling or shading pattern cannot be used as a color space in a shading pattern
a.title.is.not.a.layer=A title is not a layer
addcell.cell.has.null.value=addCell - cell has null-value
addcell.error.in.reserve=addCell - error in reserve
addcell.illegal.column.argument=addCell - illegal column argument
addcell.null.argument=addCell - null argument
addcell.only.cells.or.tables.allowed=addCell - only Cells or Tables allowed
addcell.point.has.null.value=addCell - point has null-value
adding.a.cell.at.the.location.1.2.with.a.colspan.of.3.and.a.rowspan.of.4.is.illegal.beyond.boundaries.overlapping=Adding a cell at the location ({1},{2}) with a colspan of {3} and a rowspan of {4} is illegal (beyond boundaries/overlapping).
add.image.exception=Exception adding image with path {1}
afrelationship.value.shall.be.alternative=AFRelationship value shall be Alternative.
ai.not.found.1=AI not found: ({1})
ai.too.short.1=AI too short: ({1})
all.colour.channels.in.the.jpeg2000.data.shall.have.the.same.bit-depth=All colour channels in the JPEG2000 data shall have the same bit-depth.
all.fill.bits.preceding.eol.code.must.be.0=All fill bits preceding EOL code must be 0.
all.halftones.shall.have.halftonetype.1.or.5=All halftones in a conforming PDF/A file shall have the value 1 or 5 for the HalftoneType key.
all.the.fonts.must.be.embedded.this.one.isn.t.1=All the fonts must be embedded. This one isn't: {1}
already.attempted.a.read.on.this.jbig2.file=already attempted a read() on this Jbig2 File
alt.entry.should.specify.alternate.description.for.1.element=Alt entry should specify alternate description for {1} element.
an.annotation.dictionary.shall.contain.the.f.key=An annotation dictionary shall contain the F key.
an.annotation.dictionary.shall.not.contain.the.ca.key.with.a.value.other.than.1=An annotation dictionary shall not contain the CA key with a value other than 1.0.
an.appearance.was.requested.without.a.variable.text.field=An appearance was requested without a variable text field.
an.extgstate.dictionary.shall.not.contain.the.tr.key=An ExtGState dictionary shall not contain the TR key.
an.extgstate.dictionary.shall.not.contain.the.htp.key=An ExtGState dictionary shall not contain the HTP key.
an.extgstate.dictionary.shall.not.contain.the.halftonename.key=An ExtGState dictionary shall not contain the HalftoneName key.
an.extgstate.dictionary.shall.contain.the.halftonetype.key.of.value.1.or.5=An ExtGState dictionary shall contain the HalftoneType key of value 1 or 5.
an.extgstate.dictionary.shall.not.contain.the.TR2.key.with.a.value.other.than.default=An ExtGState dictionary shall not contain the TR2 key with a value other than Default.
an.image.dictionary.shall.not.contain.alternates.key=An Image dictionary shall not contain Alternates key.
an.image.dictionary.shall.not.contain.opi.key=An Image dictionary shall not contain OPI key.
an.image.mask.cannot.contain.another.image.mask=An image mask cannot contain another image mask.
an.uncolored.pattern.was.expected=An uncolored pattern was expected.
an.uncolored.tile.pattern.can.not.have.another.pattern.or.shading.as.color=An uncolored tile pattern can not have another pattern or shading as color.
annotation.of.type.1.should.have.contents.key=Annotation of type {1} should have Contents key.
annotation.type.1.not.allowed=Annotation type {1} not allowed.
annotation.type.not.supported.flattening=This annotation is not supported for flattening. Skipping this annotation.
appearance.dictionary.of.widget.subtype.and.btn.field.type.shall.contain.only.the.n.key.with.dictionary.value=Appearance dictionary of Widget subtype and Btn field type shall contain only the n key with dictionary value
appearance.dictionary.shall.contain.only.the.n.key.with.stream.value=Appearance dictionary shall contain only the N key with stream value.
append.mode.does.not.support.changing.the.encryption.status=Append mode does not support changing the encryption status.
append.mode.requires.a.document.without.errors.even.if.recovery.was.possible=Append mode requires a document without errors even if recovery was possible.
authenticated.attribute.is.missing.the.digest=Authenticated attribute is missing the digest.
b.type.before.end.of.paragraph.at.index.1=B type before end of paragraph at index: {1}
bad.certificate.and.key=Bad certificate and key.
bad.endianness.tag.not.0x4949.or.0x4d4d=Bad endianness tag (not 0x4949 or 0x4d4d).
bad.linebreak.1.at.index.2=bad linebreak: {1} at index: {2}
bad.magic.number.should.be.42=Bad magic number, should be 42.
bad.user.password=Bad user password
badly.formated.directory.string=badly formated directory string
badly.formed.ucc.string.1=Badly formed UCC string: {1}
base64.input.not.properly.padded=Base64 input not properly padded.
bbox.must.be.a.4.element.array=BBox must be a 4 element array.
bits.per.component.must.be.1.2.4.or.8=Bits-per-component must be 1, 2, 4, or 8.
bits.per.sample.1.is.not.supported=Bits per sample {1} is not supported.
blend.mode.1.not.allowed=Blend mode {1} not allowed.
bookmark.end.tag.out.of.place=Bookmark end tag out of place.
both.colors.must.be.of.the.same.type=Both colors must be of the same type.
buffersize.1=bufferSize {1}
can.only.push.back.one.byte=Can only push back one byte
can.t.add.1.to.multicolumntext.with.complex.columns=Can't add {1} to MultiColumnText with complex columns
can.t.decode.pkcs7signeddata.object=can't decode PKCS7SignedData object
can.t.find.page.size.1=Can't find page size {1}
can.t.find.signing.certificate.with.serial.1=Can't find signing certificate with serial {1}
can.t.read.document.structure=Can't read document structure
cannot.change.destination.of.external.link=Cannot change destination of external link
cannot.handle.box.sizes.higher.than.2.32=Cannot handle box sizes higher than 2^32
cf.not.found.encryption=/CF not found (encryption)
codabar.must.have.at.least.a.start.and.stop.character=Codabar must have at least a start and stop character.
codabar.must.have.one.of.abcd.as.start.stop.character=Codabar must have one of 'ABCD' as start/stop character.
color.value.outside.range.0.255=Color value outside range 0-255.
color.not.found=Color '{1}' not found.
colors.are.not.allowed.in.uncolored.tile.patterns=Colors are not allowed in uncolored tile patterns.
colorspace.calrgb.is.not.allowed=Colorspace CalRGB is not allowed.
colorspace.rgb.is.not.allowed=Colorspace RGB is not allowed.
column.coordinate.of.location.must.be.gt.eq.0.and.lt.nr.of.columns=column coordinate of location must be >= 0 and < nr of columns
columntext.go.with.simulate.eq.eq.false.and.text.eq.eq.null=ColumnText.go with simulate==false and text==null.
components.must.be.1.3.or.4=Components must be 1, 3, or 4.
compression.jpeg.is.only.supported.with.a.single.strip.this.image.has.1.strips=Compression JPEG is only supported with a single strip. This image has {1} strips.
conflict.in.classmap=Conflict in ClassMap {1}.
conflict.in.rolemap=Conflict in RoleMap {1}.
content.can.not.be.added.to.a.pdfimportedpage=Content can not be added to a PdfImportedPage.
content.was.already.written.to.the.output=Content was already written to the output.
corrupted.png.file=Corrupted PNG file.
could.not.find.web.browser=Could not find web browser.
could.not.flatten.file.untagged.annotations.found=Could not flatten file: untagged annotations found!
count.of.referred.to.segments.had.bad.value.in.header.for.segment.1.starting.at.2=count of referred-to segments had bad value in header for segment {1} starting at {2}
creating.page.stamp.not.allowed.for.tagged.reader=Creating PageStamp not allowed for tagged reader.
crypt.filter.is.not.permitted.inline.image=Crypt filter is not permitted for inline images.
defaultcryptfilter.not.found.encryption=/DefaultCryptFilter not found (encryption)
deprecated.setstate.and.noop.actions.are.not.allowed=Deprecated set-state and no-op actions are not allowed.
destination.end.tag.out.of.place=Destination end tag out of place.
destoutputprofile.in.the.pdfa1.outputintent.dictionary.shall.be.rgb=DestOutputProfile in the PDF/A-1 OutputIntent dictionary shall be RGB.
devicecmyk.may.be.used.only.if.the.file.has.a.cmyk.pdfa.outputIntent=DeviceCMYK may be used only if the file has a PDF/A OutputIntent that uses a CMYK colour space.
devicecmyk.shall.only.be.used.if.defaultcmyk.pdfa.or.outputintent=DeviceCMYK shall only be used if a device independent DefaultCMYK colour space has been set or if a DeviceN-based DefaultCMYK colour space has been set when the DeviceCMYK colour space is used or the file has a PDF/A OutputIntent that contains a CMYK destination profile.
devicegray.may.be.used.only.if.the.file.has.a.rgb.or.cmyk.pdfa.outputIntent=DeviceGray may be used only if the file has a PDF/A OutputIntent that uses an RGB or CMYK colour space.
devicegray.shall.only.be.used.if.defaultgray.pdfa.or.outputintent=DeviceGray shall only be used if a device independent DefaultGray colour space has been set when the DeviceGray colour space is used, or if a PDF/A OutputIntent is present.
devicergb.and.devicecmyk.colorspaces.cannot.be.used.both.in.one.file=DeviceRGB and DeviceCMYK colorspaces cannot be used both in one file.
devicergb.may.be.used.only.if.the.file.has.a.rgb.pdfa.outputIntent=DeviceRGB may be used only if the file has a PDF/A OutputIntent that uses an RGB colour space.
devicergb.shall.only.be.used.if.defaultrgb.pdfa.or.outputintent=DeviceRGB shall only be used if a device independent DefaultRGB colour space has been set when the DeviceRGB colour space is used, or if the file has a PDF/A OutputIntent that contains an RGB destination profile.
devicen.color.shall.have.the.same.number.of.colorants.as.the.destination.DeviceN.color.space=DeviceN color shall have the same number of colorants as the destination color space
devicen.component.names.shall.be.different=DeviceN component names shall all be different from one another, except for the name None
dictionary.key.1.is.not.a.name=Dictionary key {1} is not a name.
dictionary.key.is.not.a.name=Dictionary key is not a name.
different.pdf.a.version=Different PDF/A version.
dimensions.of.a.cell.are.attributed.automagically.see.the.faq=Dimensions of a Cell are attributed automagically. See the FAQ.
dimensions.of.a.cell.can.t.be.calculated.see.the.faq=Dimensions of a Cell can't be calculated. See the FAQ.
dimensions.of.a.table.can.t.be.calculated.see.the.faq=Dimensions of a Table can't be calculated. See the FAQ.
directory.number.too.large=Directory number too large.
document.1.has.already.been.added=Document {1} has already been added.
document.already.pre.closed=Document already pre closed.
document.catalog.dictionary.shall.include.a.markinfo.dictionary.whose.entry.marked.shall.have.a.value.of.true=Document catalog dictionary shall include a MarkInfo dictionary whose entry Marked shall have a value of true.
document.catalog.dictionary.should.contain.lang.entry=Document catalog dictionary should contain Lang entry.
document.fields.cannot.be.copied.in.tagged.mode=Document fields cannot be copied in tagged mode.
ef.key.of.file.specification.dictionary.shall.contain.dictionary.with.valid.f.key=EF key of the file specification dictionary for an embedded file shall contain dictionary with valid F key.
element.not.allowed=Element not allowed.
embedded.file.shall.contain.valid.params.key=Embedded file shall contain valid Params key.
embedded.file.shall.contain.params.key.with.valid.moddate.key=Embedded file shall contain Params key with valid ModDate key.
embedded.file.shall.contain.pdf.mime.type=Embedded file shall contain correct pdf mime type.
embedded.files.are.not.permitted=Embedded files are not permitted.
encryption.can.only.be.added.before.opening.the.document=Encryption can only be added before opening the document.
eol.code.word.encountered.in.black.run=EOL code word encountered in Black run.
eol.code.word.encountered.in.white.run=EOL code word encountered in White run.
error.attempting.to.launch.web.browser=Error attempting to launch web browser
error.expected.hex.character.and.not.char.thenextbyte.1=Error: expected hex character and not (char)theNextByte:{1}
error.expected.the.end.of.a.dictionary=Error: expected the end of a dictionary.
error.in.attribute.processing=Error in attribute processing
error.in.base64.code.reading.stream=Error in Base64 code reading stream.
error.parsing.cmap.beginbfchar.expected.cosstring.or.cosname.and.not.1=Error parsing CMap beginbfchar, expected {COSString or COSName} and not {1}
error.reading.objstm=Error reading ObjStm
error.reading.string=Error reading string
error.resolving.freetext.font=Cannot resolve annotation's font. It won't be flattened
error.with.jp.marker=Error with JP Marker
every.annotation.shall.have.at.least.one.appearance.dictionary=Every annotation shall have at least one appearance dictionary
exactly.one.colour.space.specification.shall.have.the.value.0x01.in.the.approx.field=Exactly one colour space specification shall have the value 0x01 in the APPROX field.
expected.ftyp.marker=Expected FTYP Marker
expected.gt.for.tag.lt.1.gt=Expected > for tag: <{1}/>
expected.ihdr.marker=Expected IHDR Marker
expected.colr.marker=Expected COLR Marker
expected.jp.marker=Expected JP Marker
expected.jp2h.marker=Expected JP2H Marker
extra.samples.are.not.supported=Extra samples are not supported.
failed.to.get.tsa.response.from.1=Failed to get TSA response from '{1}'
fdf.header.not.found=FDF header signature not found.
field.flattening.is.not.supported.in.append.mode=Field flattening is not supported in append mode.
field.names.cannot.contain.a.dot=Field names cannot contain a dot.
file.header.flags.bits.2.7.not.0=file header flags bits 2-7 not 0
file.header.idstring.not.good.at.byte.1=file header idstring not good at byte {1}
file.is.not.a.valid.png=File is not a valid PNG.
file.position.0.cross.reference.entry.in.this.xref.subsection=File position 0 cross-reference entry in this xref subsection
file.specification.dictionary.shall.contain.correct.afrelationship.key=The file specification dictionary for an embedded file shall contain correct AFRelationship key.
file.specification.dictionary.shall.contain.f.uf.and.desc.entries=The file specification dictionary for an embedded file shall contain the F and UF keys and should contain the Desc key.
filter.ccittfaxdecode.is.only.supported.for.images=Filter CCITTFaxDecode is only supported for images
first.scanline.must.be.1d.encoded=First scanline must be 1D encoded.
font.1.with.2.encoding.is.not.a.cjk.font=Font '{1}' with '{2}' encoding is not a CJK font.
font.1.with.2.is.not.recognized=Font '{1}' with '{2}' is not recognized.
font.and.size.must.be.set.before.writing.any.text=Font and size must be set before writing any text
font.size.too.small.1=Font size too small: {1}
fontfactoryimp.cannot.be.null=FontFactoryImp cannot be null.
freetext.annotation.doesnt.contain.da=FreeText Annotation doesn't contain a DA. Not flattening this annotation.
freetext.flattening.is.not.supported.in.append.mode=FreeText flattening is not supported in append mode.
annotation.flattening.is.not.supported.in.append.mode=Annotation flattening is not supported in append mode.
getcell.at.illegal.index.1.max.is.2=getCell at illegal index :{1} max is {2}
getcell.at.illegal.index.1=getCell at illegal index : {1}
gif.signature.nor.found=Gif signature nor found.
graphics.state.stack.depth.is.greater.than.28=Graphics state stack depth is greater than 28.
greaterthan.not.expected='>' not expected
halftones.shall.not.contains.halftonename=Halftones in a conforming PDF/A-2 file shall not contain a HalftoneName key.
handler.1.already.registered=XObject handler {1} already registered
if.device.rgb.cmyk.gray.used.in.file.that.file.shall.contain.pdfa.outputintent=If an uncalibrated colour space(DeviceRGB/DeviceCMYK/DeviceGray) is used in a file, then that file shall contain a PDF/A OutputIntent.
if.outputintents.array.more.than.one.entry.the.same.indirect.object=If a file's OutputIntents array contains more than one entry, then all entries that contain a DestOutputProfile key shall have as the value of that key the same indirect object.
if.the.document.not.contain.outputintent.transparencygroup.shall.comtain.cs.key=If the document does not contain a PDF/A OutputIntent, then all Page objects that contain transparency shall include the Group key, and the attribute dictionary that forms the value of that Group key shall include a CS entry whose value shall be used as the default blending colour space.
illegal.capacity.1=Illegal Capacity: {1}
illegal.character.in.ascii85decode=Illegal character in ASCII85Decode.
illegal.character.in.asciihexdecode=Illegal character in ASCIIHexDecode.
illegal.length.in.ascii85decode=Illegal length in ASCII85Decode.
illegal.length.value=Illegal Length value.
illegal.load.1=Illegal Load: {1}
illegal.p.value=Illegal P value.
illegal.paragraph.embedding.level.1=illegal paragraph embedding level: {1}
illegal.r.value=Illegal R value.
illegal.type.value.at.1.2=illegal type value at {1}: {2}
illegal.v.value=Illegal V value.
illegal.value.for.predictor.in.tiff.file=Illegal value for Predictor in TIFF file.
illegal.ve.value=Illegal VE value.
illegal.pages.tree=Illegal pages tree.
illegal.resources.tree=Illegal Resources Tree.
improperly.padded.base64.input=Improperly padded Base64 input.
in.a.page.label.the.page.numbers.must.be.greater.or.equal.to.1=In a page label the page numbers must be greater or equal to 1.
in.codabar.start.stop.characters.are.only.allowed.at.the.extremes=In codabar, start/stop characters are only allowed at the extremes.
incompatible.pdf.a.conformance.level=Incompatible PDF/A conformance level.
incomplete.palette=incomplete palette
inconsistent.mapping=Inconsistent mapping.
inconsistent.writers.are.you.mixing.two.documents=Inconsistent writers. Are you mixing two documents?
incorrect.segment.type.in.1=Incorrect segment type in {1}
infinite.table.loop=Infinite table loop; row content is larger than the page (for instance because you didn't scale an image).
inline.elements.with.role.null.are.not.allowed=The tagging mode markInlineElementsOnly is used. Inline elements with role 'null' are not allowed.
insertion.of.illegal.element.1=Insertion of illegal Element: {1}
inserttable.point.has.null.value=insertTable - point has null-value
inserttable.table.has.null.value=insertTable - table has null-value
inserttable.wrong.columnposition.1.of.location.max.eq.2=insertTable -- wrong columnposition({1}) of location; max ={2}
insufficient.data=Insufficient data.
insufficient.length=Insufficient length.
internal.inconsistence=Internal inconsistence.
inthashtableiterator=IntHashtableIterator
invalid.additional.action.type.1=Invalid additional action type: {1}
invalid.additional.action.type.1=Invalid additional action type: {1}
invalid.ai.length.1=Invalid AI length: ({1})
invalid.border.style=Invalid border style.
invalid.character.in.base64.data=Invalid character in Base64 data.
invalid.code.encountered.while.decoding.2d.group.3.compressed.data=Invalid code encountered while decoding 2D group 3 compressed data.
invalid.code.encountered.while.decoding.2d.group.4.compressed.data=Invalid code encountered while decoding 2D group 4 compressed data.
invalid.code.encountered=Invalid code encountered.
invalid.code.type=Invalid code type.
invalid.codeword.size=Invalid codeword size.
invalid.color.type=Invalid color type.
invalid.cross.reference.entry.in.this.xref.subsection=Invalid cross-reference entry in this xref subsection
invalid.end.tag.1=Invalid end tag - {1}
invalid.generation.number=Invalid generation number.
invalid.http.response.1=Invalid HTTP response: {1}
invalid.icc.profile=Invalid ICC profile
invalid.index.1=Invalid index: {1}
invalid.listtype.value=Invalid listType value.
invalid.magic.value.for.bmp.file=Invalid magic value for BMP file.
invalid.named.action=Invalid named action.
invalid.object.number=Invalid object number.
invalid.page.additional.action.type.1=Invalid page additional action type: {1}
invalid.page.number.1=Invalid page number: {1}
invalid.run.direction.1=Invalid run direction: {1}
invalid.status.1=Invalid status: {1}
invalid.tsa.1.response.code.2=Invalid TSA '{1}' response, code {2}
invalid.type.was.passed.in.state.1=Invalid type was passed in state: {1}
invalid.use.of.a.pattern.a.template.was.expected=Invalid use of a pattern. A template was expected.
irregular.columns.are.not.supported.in.composite.mode=Irregular columns are not supported in composite mode.
it.is.not.possible.to.free.reader.in.merge.fields.mode=It is not possible to free reader in mergeFields mode.
java.awt.image.fetch.aborted.or.errored=java.awt.Image fetch aborted or errored
java.awt.image.interrupted.waiting.for.pixels=java.awt.Image Interrupted waiting for pixels!
jpeg2000.enumerated.colour.space.19.(CIEJab).shall.not.be.used=JPEG2000 enumerated colour space 19 (CIEJab) shall not be used.
key.is.null=key is null.
keyword.encrypt.shall.not.be.used.in.the.trailer.dictionary=Keyword Encrypt shall not be used in the trailer dictionary.
lab.cs.black.point=The BlackPoint entry in Lab color space could be only an array of three numbers [XB YB ZB]. All three of these numbers shall be non-negative. Default value: [0.0 0.0 0.0].
lab.cs.range=The Range entry in Lab color space could be only an array of four numbers [amin amax bmin bmax]. Default value: [-100 100 -100 100].
lab.cs.white.point=The WhitePoint array (three numbers [XW YW ZW]) is required in Lab color space. The numbers XW and ZW shall be positive, and YW shall be 1.0.
last.linebreak.must.be.at.1=last linebreak must be at {1}
launch.sound.movie.resetform.importdata.and.javascript.actions.are.not.allowed=Launch, Sound, Movie, ResetForm, ImportData, Hide, SetOCGState, Rendition, Trans, GoTo3DView and JavaScript actions are not allowed.
layers.are.not.allowed=Layers are not allowed.
layout.out.of.bounds=Layout out of bounds.
line.iterator.out.of.bounds=line iterator out of bounds
linear.page.mode.can.only.be.called.with.a.single.parent=Linear page mode can only be called with a single parent.
lzwdecode.filter.is.not.permitted=LZWDecode filter is not permitted.
lzw.flavour.not.supported=LZW flavour not supported.
macrosegmentid.must.be.gt.eq.0=macroSegmentId must be >=0
macrosegmentid.must.be.lt.macrosemgentcount=macroSegmentId must be < macroSemgentCount
macrosemgentcount.must.be.gt.0=macroSemgentCount must be > 0
make.copy.of.catalog.dictionary.is.forbidden=Make copy of Catalog dictionary is forbidden.
mapping.code.should.be.1.or.two.bytes.and.not.1=Mapping code should be 1 or two bytes and not {1}
missing.end.tag=Missing end tag
missing.endcharmetrics.in.1=Missing EndCharMetrics in {1}
missing.endfontmetrics.in.1=Missing EndFontMetrics in {1}
missing.endkernpairs.in.1=Missing EndKernPairs in {1}
missing.startcharmetrics.in.1=Missing StartCharMetrics in {1}
missing.tag.s.for.ojpeg.compression=Missing tag(s) for OJPEG compression.
multicolumntext.has.no.columns=MultiColumnText has no columns
name.end.tag.out.of.place=Name end tag out of place.
named.action.type.1.not.allowed=Named action type {1} not allowed.
needappearances.flag.of.the.interactive.form.dictionary.shall.either.not.be.present.or.shall.be.false=NeedAppearances flag of the interactive form dictionary shall either not be present or shall be false.
nested.tags.are.not.allowed=Nested tags are not allowed.
no.compatible.encryption.found=No compatible encryption found
no.error.just.an.old.style.table=No error, just an old style table
no.font.is.defined=No font is defined.
no.glyphs.defined.for.type3.font=No glyphs defined for Type3 font
no.keys.other.than.UR3.and.DocMDP.shall.be.present.in.a.permissions.dictionary=No keys other than UR3 and DocMDP shall be present in a permissions dictionary.
no.structtreeroot.found=No StructTreeRoot found, this probably isn't a tagged PDF document!
no.valid.column.line.found=No valid column line found.
no.valid.encryption.mode=No valid encryption mode
not.a.placeable.windows.metafile=Not a placeable windows metafile
not.a.valid.jpeg2000.file=Not a valid Jpeg2000 file
not.a.valid.pfm.file=Not a valid PFM file.
not.a.valid.pkcs.7.object.not.a.sequence=Not a valid PKCS#7 object - not a sequence
not.a.valid.pkcs.7.object.not.signed.data=Not a valid PKCS#7 object - not signed data
not.all.annotations.could.be.added.to.the.document.the.document.doesn.t.have.enough.pages=Not all annotations could be added to the document (the document doesn't have enough pages).
not.colorized.typed3.fonts.only.accept.mask.images=Not colorized Typed3 fonts only accept mask images.
not.identity.crypt.filter.is.not.permitted=Not Identity Crypt filter is not permitted.
null.outpustream=null OutputStream
number.of.entries.in.this.xref.subsection.not.found=Number of entries in this xref subsection not found
object.number.of.the.first.object.in.this.xref.subsection.not.found=Object number of the first object in this xref subsection not found
ocsp.status.is.revoked=OCSP Status is revoked!
ocsp.status.is.unknown=OCSP Status is unknown!
only.bmp.can.be.wrapped.in.wmf=Only BMP can be wrapped in WMF.
only.bmp.png.wmf.gif.and.jpeg.images.are.supported.by.the.rtf.writer=Only BMP, PNG, WMF, GIF and JPEG images are supported by the RTF Writer
only.javascript.actions.are.allowed=Only JavaScript actions are allowed.
only.jpx.baseline.set.of.features.shall.be.used=Only JPX baseline set of features shall be used.
only.one.of.artbox.or.trimbox.can.exist.in.the.page=Only one of ArtBox or TrimBox can exist in the page.
only.pdfa.documents.can.be.added.in.PdfACopy=Only PDF/A documents can be added in PdfACopy.
only.pdfa.documents.can.be.opened.in.PdfAStamper=Only PDF/A documents can be opened in PdfAStamper.
only.pdfa.1.documents.can.be.opened.in.PdfAStamper=This instance of PdfAStamper is configured to process PDF/A-{1} documents.
only.pdflayer.is.accepted=Only PdfLayer is accepted.
only.rgb.gray.and.cmyk.are.supported.as.alternative.color.spaces=Only RGB, Gray and CMYK are supported as alternative color spaces.
open.actions.by.name.are.not.supported=Open actions by name are not supported.
operator.1.already.registered=Operator {1} already registered
optional.content.configuration.dictionary.shall.contain.name.entry=Optional content configuration dictionary shall contain Name entry.
order.array.shall.contain.references.to.all.ocgs=Order array shall contain references to all OCGs.
outputintent.shall.be.prtr.or.mntr=A PDF/A outputintent profile shall be an output profile (Device Class = “prtr”) or a monitor profile (Device Class = “mntr”).
outputintent.shall.have.colourspace.gray.rgb.or.cmyk=A PDF/A outputintent profile shall have a colour space of either “GRAY”, “RGB”, or “CMYK”.
outputintent.shall.have.gtspdfa1.and.destoutputintent=A PDF/A OutputIntent dictionary shall have GTS_PDFA1 as the value of its S key and a valid ICC profile stream as the value of its DestOutputProfile key.
outputintent.shall.not.be.updated=PDF/A OutputIntent shall not be updated. The original PDF could contain device dependent colors that need the current OutputIntent.
page.1.invalid.for.segment.2.starting.at.3=page {1} invalid for segment {2} starting at {3}
page.attribute.missing=Page attribute missing.
page.dictionary.shall.not.include.aa.entry=The page dictionary shall not include an AA entry.
page.dictionary.shall.not.include.pressteps.entry=The page dictionary shall not include a PresSteps entry.
page.not.found=Page not found.
page.reordering.requires.a.single.parent.in.the.page.tree.call.pdfwriter.setlinearmode.after.open=Page reordering requires a single parent in the page tree. Call PdfWriter.setLinearMode() after open.
page.reordering.requires.an.array.with.the.same.size.as.the.number.of.pages=Page reordering requires an array with the same size as the number of pages.
page.reordering.requires.no.page.repetition.page.1.is.repeated=Page reordering requires no page repetition. Page {1} is repeated.
page.reordering.requires.pages.between.1.and.1.found.2=Page reordering requires pages between 1 and {1}. Found {2}.
partial.form.flattening.is.not.supported.with.xfa.forms=Partial form flattening is not supported with XFA forms.
pdf.array.exceeds.length.set.by.PDFA1.standard=PDF array exceeds length set by the PDF/A1 standard: {1}. Maximum length is 8191.
pdf.array.is.out.of.bounds=PDF array is out of bounds.
pdf.dictionary.is.out.of.bounds=PDF dictionary is out of bounds.
pdf.header.not.found=PDF header signature not found.
pdf.name.is.too.long=PDF name is too long.
pdf.startxref.not.found=PDF startxref not found.
pdf.string.is.too.long=PDF string is too long.
pdfpcells.can.t.have.a.rowspan.gt.1=PdfPCells can't have a rowspan > 1
pdfreader.not.opened.with.owner.password=PdfReader not opened with owner password
pdfx.conformance.can.only.be.set.before.opening.the.document=PDFX conformance can only be set before opening the document.
pdfx.conformance.cannot.be.set.for.PdfAStamperImp.instance=PDFX conformance cannot be set for PdfAStamperImp instance.
pdfx.conformance.cannot.be.set.for.PdfAWriter.instance=PDFX conformance cannot be set for PdfAWriter instance.
planar.images.are.not.supported=Planar images are not supported.
png.filter.unknown=PNG filter unknown.
postscript.xobjects.are.not.allowed=PostScript XObjects are not allowed.
preclose.must.be.called.first=preClose() must be called first.
premature.end.in.1=Premature end in {1}
premature.end.of.file=Premature end of file.
premature.eof.while.reading.jpg=Premature EOF while reading JPG.
real.number.is.out.of.range=Real number is out of range.
rebuild.failed.1.original.message.2=Rebuild failed: {1}; Original message: {2}
rectanglereadonly.this.rectangle.is.read.only=RectangleReadOnly: this Rectangle is read only.
reference.pointing.to.reference=Reference pointing to reference.
referring.to.widht.height.of.page.we.havent.seen.yet.1=referring to widht/height of page we havent seen yet? {1}
remove.not.supported=remove() not supported.
reserve.incorrect.column.size=reserve - incorrect column/size
resources.do.not.contain.extgstate.entry.unable.to.process.operator.1=Resources do not contain ExtGState entry. Unable to process operator {1}
root.element.is.not.bookmark.1=Root element is not Bookmark: {1}
root.element.is.not.destination=Root element is not Destination.
root.element.is.not.xfdf.1=Root element is not xfdf: {1}
rotation.must.be.a.multiple.of.90=Rotation must be a multiple of 90.
row.coordinate.of.location.must.be.gt.eq.0=row coordinate of location must be >= 0
scanline.must.begin.with.eol.code.word=Scanline must begin with EOL code word.
separations.patterns.and.shadings.are.not.allowed.in.mk.dictionary=Separations, patterns and shadings are not allowed in MK dictionary.
setelement.position.already.taken=setElement - position already taken
signature.references.dictionary.shall.not.contain.digestlocation.digestmethod.digestvalue=The Signature References dictionary shall not contain the keys DigestLocation, DigestMethod and DigestValue.
start.marker.missing.in.1=Start marker missing in {1}
startxref.is.not.followed.by.a.number=startxref is not followed by a number.
startxref.not.found=startxref not found.
stdcf.not.found.encryption=/StdCF not found (encryption)
stream.could.not.be.compressed.filter.is.not.a.name.or.array=Stream could not be compressed: filter is not a name or array.
stream.object.dictionary.shall.not.contain.the.f.ffilter.or.fdecodeparams.keys=Stream object dictionary shall not contain the F, FFilter or FDecodeParams keys.
structparent.not.found=StructParent not found.
structparentid.not.found=StructParent ID not found.
support.only.sha1.hash.algorithm=Support only SHA1 hash algorithm.
support.only.rsa.and.dsa.algorithms=Support only RSA and DSA algorithms.
invalid.structparent=Invalid StructParent.
table.1.does.not.exist.in.2=Table '{1}' does not exist in {2}
tag.1.not.allowed=Tag {1} not allowed.
tagging.must.be.set.before.opening.the.document=Tagging must be set before opening the document.
template.with.tagged.could.not.be.used.more.than.once=Template with tagged content could not be used more than once.
text.annotations.should.set.the.nozoom.and.norotate.flag.bits.of.the.f.key.to.1=Text annotations should set the NoZoom and NoRotate flag bits of the F key to 1.
text.cannot.be.null=Text cannot be null.
the.array.must.contain.string.or.pdfannotation=The array must contain String or PdfAnnotation.
the.artifact.type.1.is.invalid=The artifact type '{1}' is invalid.
the.as.key.shall.not.appear.in.any.optional.content.configuration.dictionary=The AS key shall not appear in any optional content configuration dictionary.
the.bit-depth.of.the.jpeg2000.data.shall.have.a.value.in.the.range.1to38=The bit-depth of the JPEG2000 data shall have a value in the range 1 to 38.
the.byte.array.is.not.a.recognized.imageformat=The byte array is not a recognized imageformat.
the.ccitt.compression.type.must.be.ccittg4.ccittg3.1d.or.ccittg3.2d=The CCITT compression type must be CCITTG4, CCITTG3_1D or CCITTG3_2D
the.char.1.doesn.t.belong.in.this.type3.font=The char {1} doesn't belong in this Type3 font
the.char.1.is.not.defined.in.a.type3.font=The char {1} is not defined in a Type3 font
the.character.1.is.illegal.in.codabar=The character '{1}' is illegal in codabar.
the.character.1.is.illegal.in.code.39.extended=The character '{1}' is illegal in code 39 extended.
the.character.1.is.illegal.in.code.39=The character '{1}' is illegal in code 39.
the.cmap.1.does.not.exist.as.a.resource=The cmap {1} does not exist as a resource.
the.cmap.1.was.not.found=The Cmap {1} was not found.
the.compression.1.is.not.supported=The compression {1} is not supported.
the.document.catalog.dictionary.shall.contain.metadata=The document catalog dictionary of a PDF/A conforming file shall contain the Metadata key
the.document.catalog.dictionary.shall.not.include.an.aa.entry=The document catalog dictionary shall not include an AA entry.
the.document.catalog.dictionary.shall.not.include.alternatepresentation.names.entry=The document catalog dictionary shall not contains an AlternatePresentation entry in the Names entry.
the.document.catalog.dictionary.shall.not.include.a.needrendering.entry=The document catalog dictionary shall not include NeedRendering entry.
the.document.catalog.dictionary.shall.not.include.a.requirements.entry=The document catalog dictionary shall not include Requirements entry.
the.document.catalog.dictionary.shall.not.include.acroform.xfa.entry=The document catalog dictionary shall not contains a XFA entry in the AcroForm entry.
the.document.catalog.dictionary.shall.not.include.embeddedfiles.names.entry=The document catalog dictionary shall not contain an EmbeddedFiles entry in the Names entry.
the.document.does.not.contain.parenttree=The document does not contain ParentTree.
the.document.has.been.closed.you.can.t.add.any.elements=The document has been closed. You can't add any Elements.
the.document.has.no.catalog.object=The document has no catalog object (meaning: it's an invalid PDF).
the.document.has.no.page.root=The document has no page root (meaning: it's an invalid PDF).
the.document.has.no.pages=The document has no pages.
the.document.is.not.open.yet.you.can.only.add.meta.information=The document is not open yet; you can only add Meta information.
the.document.is.not.open=The document is not open.
the.document.is.open.you.can.only.add.elements.with.content=The document is open; you can only add Elements with content.
the.document.must.be.open.to.import.rtf.documents=The document must be open to import RTF documents.
the.document.must.be.open.to.import.rtf.fragments=The document must be open to import RTF fragments.
the.document.was.reused=The document was reused.
the.export.and.the.display.array.must.have.the.same.size=The export and the display array must have the same size.
the.f.keys.print.flag.bit.shall.be.set.to.1.and.its.hidden.invisible.and.noview.flag.bits.shall.be.set.to.0=The F key's Print flag bit shall be set to 1 and its Hidden, Invisible and NoView flag bits shall be set to 0.
the.f.keys.print.flag.bit.shall.be.set.to.1.and.its.hidden.invisible.noview.and.togglenoview.flag.bits.shall.be.set.to.0=The F key's Print flag bit shall be set to 1 and its Hidden, Invisible, NoView and ToggleNoView flag bits shall be set to 0.
the.field.1.already.exists=The field {1} already exists.
the.field.1.does.not.exist=The field {1} does not exist.
the.field.1.is.not.a.signature.field=The field {1} is not a signature field.
the.file.does.not.contain.any.valid.image=The file does not contain any valid image.
the.filter.1.is.not.supported=The filter {1} is not supported.
the.font.index.for.1.must.be.between.0.and.2.it.was.3=The font index for {1} must be between 0 and {2}. It was {3}.
the.font.index.for.1.must.be.positive=The font index for {1} must be positive.
the.image.mask.is.not.a.mask.did.you.do.makemask=The image mask is not a mask. Did you do makeMask()?
the.image.must.have.absolute.positioning=The image must have absolute positioning.
the.key.1.didn.t.reserve.space.in.preclose=The key {1} didn't reserve space in preClose().
the.key.1.is.too.big.is.2.reserved.3=The key {1} is too big. Is {2}, reserved {3}
the.layer.1.already.has.a.parent=The layer '{1}' already has a parent.
the.matrix.size.must.be.6=The matrix size must be 6.
the.name.1.is.too.long.2.characters=The name '{1}' is too long ({2} characters).
the.new.size.must.be.positive.and.lt.eq.of.the.current.size=The new size must be positive and <= of the current size
the.number.of.booleans.in.this.array.doesn.t.correspond.with.the.number.of.fields=The number of booleans in this array doesn't correspond with the number of fields.
the.number.of.columns.in.pdfptable.constructor.must.be.greater.than.zero=The number of columns in PdfPTable constructor must be greater than zero.
the.number.of.colour.channels.in.the.jpeg2000.data.shall.be.123=The number of colour channels in the JPEG2000 data shall be 1, 2 or 3.
the.original.document.was.reused.read.it.again.from.file=The original document was reused. Read it again from file.
the.page.less.3.units.nor.greater.14400.in.either.direction=The size of any of the page boundaries shall not be less than 3 units in either direction, nor shall it be greater than 14 400 units in either direction.
the.page.number.must.be.gt.eq.1=The page number must be >= 1.
the.page.size.must.be.smaller.than.14400.by.14400.its.1.by.2=The page size must be smaller than 14400 by 14400. It's {1} by {2}.
the.parent.has.already.another.function=The parent has already another function.
the.photometric.1.is.not.supported=The photometric {1} is not supported.
the.resource.cjkencodings.properties.does.not.contain.the.encoding.1=The resource cjkencodings.properties does not contain the encoding {1}
the.smask.key.is.not.allowed.in.extgstate=The SMask key is not allowed in ExtGState.
the.smask.key.is.not.allowed.in.images=The /SMask key is not allowed in images.
the.spot.color.must.be.the.same.only.the.tint.can.vary=The spot color must be the same, only the tint can vary.
the.stack.is.empty=The stack is empty.
the.structure.has.kids=The structure has kids.
the.table.width.must.be.greater.than.zero=The table width must be greater than zero.
the.template.can.not.be.null=The template can not be null.
the.text.is.too.big=The text is too big.
the.text.length.must.be.even=The text length must be even.
the.two.barcodes.must.be.composed.externally=The two barcodes must be composed externally.
the.update.dictionary.has.less.keys.than.required=The update dictionary has less keys than required.
the.url.of.the.image.is.missing=The URL of the image is missing.
the.value.has.to.be.true.of.false.instead.of.1=The value has to be 'true' of 'false', instead of '{1}'.
the.value.of.interpolate.key.shall.not.be.true=The value of Interpolate key shall not be true.
the.value.of.the.meth.entry.in.colr.box.shall.be.123=The value of the METH entry in 'colr' box shall be 1, 2 or 3.
the.width.cannot.be.set=The width cannot be set.
the.widths.array.in.pdfptable.constructor.can.not.be.null=The widths array in PdfPTable constructor can not be null.
the.widths.array.in.pdfptable.constructor.can.not.have.zero.length=The widths array in PdfPTable constructor can not have zero length.
the.writer.in.pdfcontentbyte.is.null=The writer in PdfContentByte is null.
there.are.illegal.characters.for.barcode.128.in.1=There are illegal characters for barcode 128 in '{1}'.
there.are.not.enough.imported.pages.for.copied.fields=There are not enough imported pages for copied fields. Please use addDocument or copy enough pages before.
this.acrofields.instance.is.read.only=This AcroFields instance is read-only.
this.image.can.not.be.an.image.mask=This image can not be an image mask.
this.largeelement.has.already.been.added.to.the.document=This LargeElement has already been added to the Document.
this.page.cannot.be.replaced.new.content.was.already.added=This page cannot be replaced: new content was already added
this.pkcs.7.object.has.multiple.signerinfos.only.one.is.supported.at.this.time=This PKCS#7 object has multiple SignerInfos - only one is supported at this time
tiff.5.0.style.lzw.codes.are.not.supported=TIFF 5.0-style LZW codes are not supported.
tiff.fill.order.tag.must.be.either.1.or.2=TIFF_FILL_ORDER tag must be either 1 or 2.
tiles.are.not.supported=Tiles are not supported.
title.cannot.be.null=Title cannot be null.
token.obj.expected=Token 'obj' expected.
too.many.indirect.objects=Too many indirect objects.
trailer.not.found=trailer not found.
trailer.prev.entry.points.to.its.own.cross.reference.section=Trailer /Prev entry points to its own cross-reference section.
transparency.is.not.allowed.ca.eq.1=Transparency is not allowed: /ca = {1}
transparency.length.must.be.equal.to.2.with.ccitt.images=Transparency length must be equal to 2 with CCITT images
transparency.length.must.be.equal.to.componentes.2=Transparency length must be equal to (componentes * 2)
trying.to.create.a.table.without.rows=Trying to create a table without rows.
tsa.1.failed.to.return.time.stamp.token.2=TSA '{1}' failed to return time stamp token: {2}
two.byte.arrays.are.needed.if.the.type1.font.is.embedded=Two byte arrays are needed if the Type1 font is embedded.
type3.font.used.with.the.wrong.pdfwriter=Type3 font used with the wrong PdfWriter
types.is.null=types is null
unbalanced.begin.end.marked.content.operators=Unbalanced begin/end marked content operators.
unbalanced.begin.end.text.operators=Unbalanced begin/end text operators.
path.construction.operator.inside.text.object=Path construction or drawing operators aren't allowed inside a text object.
unbalanced.layer.operators=Unbalanced layer operators.
unbalanced.marked.content.operators=Unbalanced marked content operators.
unbalanced.save.restore.state.operators=Unbalanced save/restore state operators.
unexpected.color.space.in.embedded.icc.profile=Unexpected color space in embedded ICC Profile. It will be ignored.
unexpected.end.of.file=Unexpected end of file.
unexpected.eof=Unexpected EOF
unexpected.gt.gt=Unexpected '>>'
unexpected.close.bracket=Unexpected ']'
unknown.color.format.must.be.rgb.or.rrggbb=Unknown color format. Must be #RGB or #RRGGBB
unknown.encryption.type.r.eq.1=Unknown encryption type R = {1}
unknown.encryption.type.v.eq.1=Unknown encryption type V = {1}
unknown.filter.1=Unknown filter: {1}
unknown.hash.algorithm.1=Unknown Hash Algorithm {1}
unknown.image.format={1} is not a recognized imageformat.
unknown.key.algorithm.1=Unknown Key Algorithm {1}
unknown.object.at.k.1=Unknown object at /K {1}
unknown.structure.element.role.1=Unknown structure element role: {1}.
unknown=unknown
unsupported.box.size.eq.eq.0=Unsupported box size == 0
unsupported.in.this.context.use.pdfstamper.addannotation=Unsupported in this context. Use PdfStamper.addAnnotation()
use.pdfstamper.getundercontent.or.pdfstamper.getovercontent=Use PdfStamper.getUnderContent() or PdfStamper.getOverContent()
use.pdfstamper.setthumbnail=Use PdfStamper.setThumbnail().
use.setpageaction.pdfname.actiontype.pdfaction.action.int.page=Use setPageAction(PdfName actionType, PdfAction action, int page)
userunit.should.be.a.value.between.1.and.75000=UserUnit should be a value between 1 and 75000.
value.of.name.entry.shall.be.unique.amongst.all.optional.content.configuration.dictionaries=Value of Name entry shall be unique amongst all optional content configuration dictionaries
verification.already.output=Verification already output
verticaltext.go.with.simulate.eq.eq.false.and.text.eq.eq.null=VerticalText.go with simulate==false and text==null.
while.removing.wmf.placeable.header=while removing wmf placeable header
widget.annotation.dictionary.or.field.dictionary.shall.not.include.a.or.aa.entry=Widget annotation dictionary or Field dictionary shall not include an A or AA entry.
writelength.can.only.be.called.after.output.of.the.stream.body=writeLength() can only be called after output of the stream body.
writelength.can.only.be.called.in.a.contructed.pdfstream.inputstream.pdfwriter=writeLength() can only be called in a contructed PdfStream(InputStream,PdfWriter).
wrong.number.of.columns=Wrong number of columns.
xmllocator.cannot.be.null=XmlLocator cannot be null, see XmlSignatureAppearance.
XObject.1.is.not.a.stream=XObject {1} is not a stream.
xref.subsection.not.found=xref subsection not found
xstep.or.ystep.can.not.be.zero=XStep or YStep can not be ZERO.
you.can.only.add.a.writer.to.a.pdfdocument.once=You can only add a writer to a PdfDocument once.
you.can.only.add.cells.to.rows.no.objects.of.type.1=You can only add cells to rows, no objects of type {1}
you.can.only.add.objects.that.implement.the.element.interface=You can only add objects that implement the Element interface.
you.can.t.add.a.1.to.a.section=You can't add a {1} to a Section.
you.can.t.add.an.element.of.type.1.to.a.simplecell=You can't add an element of type {1} to a SimpleCell.
you.can.t.add.cells.to.a.table.directly.add.them.to.a.row.first=You can't add cells to a table directly, add them to a row first.
you.can.t.add.listitems.rows.or.cells.to.a.cell=You can't add listitems, rows or cells to a cell.
you.can.t.add.one.row.to.another.row=You can't add one row to another row.
you.can.t.have.1.pages.on.one.page.minimum.2.maximum.8=You can't have {1} pages on one page (minimum 2; maximum 8).
you.can.t.set.the.full.compression.if.the.document.is.already.open=You can't set the full compression if the document is already open.
you.can.t.set.the.initial.leading.if.the.document.is.already.open=You can't set the initial leading if the document is already open.
you.can.t.split.this.document.at.page.1.there.is.no.such.page=You can't split this document at page {1}; there is no such page.
you.can.t.translate.a.negative.number.into.an.alphabetical.value=You can't translate a negative number into an alphabetical value.
you.have.to.consolidate.the.named.destinations.of.your.reader=You have to consolidate the named destinations of your reader.
you.have.to.define.a.boolean.array.for.this.collection.sort.dictionary=You have to define a boolean array for this collection sort dictionary.
you.have.used.the.wrong.constructor.for.this.fieldpositioningevents.class=You have used the wrong constructor for this FieldPositioningEvents class.
you.must.set.a.value.before.adding.a.prefix=You must set a value before adding a prefix.
you.need.a.single.boolean.for.this.collection.sort.dictionary=You need a single boolean for this collection sort dictionary.
the.decode.parameter.type.1.is.not.supported=The decode parameter type {1} is not supported.
the.color.space.1.is.not.supported=The color space {1} is not supported.
the.color.depth.1.is.not.supported=The color depth {1} is not supported.
N.value.1.is.not.supported=N value {1} is not supported
Decoding.can't.happen.on.this.type.of.stream.(.1.)=Decoding can't happen on this type of stream ({1})
zugferd.xmp.schema.shall.contain.attachment.name=ZUGFeRD XMP schema shall contain attachment name.
invalid.reference.number.skip=Invalid reference was found. Reference will be skipped.