{"ExternalStorage": {"DefaultProvider": "MinIO", "EnableFallback": false, "FallbackOrder": ["AwsS3", "Firebase"], "MinIO": {"Endpoint": "localhost:9000", "ProxyEndpoint": "https://your-proxy-domain.com", "AccessKey": "your-access-key", "SecretKey": "your-secret-key", "UseSSL": false, "Region": "us-east-1", "DefaultBucket": "default-bucket", "TimeoutSeconds": 30, "CreateBucketIfNotExists": true}, "AwsS3": {"AccessKeyId": "your-aws-access-key-id", "SecretAccessKey": "your-aws-secret-access-key", "SessionToken": "your-aws-session-token", "Region": "us-east-1", "ServiceUrl": "https://s3.amazonaws.com", "ForcePathStyle": false, "UseAccelerateEndpoint": false, "UseDualstackEndpoint": false, "StorageClass": "STANDARD", "ServerSideEncryption": "AES256", "KmsKeyId": "your-kms-key-id", "DefaultBucket": "your-default-bucket", "TimeoutSeconds": 30, "CreateBucketIfNotExists": true}, "Firebase": {"ProjectId": "your-firebase-project-id", "ServiceAccountKeyPath": "/path/to/service-account-key.json", "ServiceAccountKeyJson": "{\"type\":\"service_account\",...}", "StorageBucket": "your-project-id.appspot.com", "AppName": "ExternalStorageApp", "UseEmulator": false, "EmulatorHost": "localhost", "EmulatorPort": 9199, "DownloadUrlDomain": "https://firebasestorage.googleapis.com", "DefaultBucket": "your-default-bucket", "TimeoutSeconds": 30, "CreateBucketIfNotExists": true}}, "Logging": {"LogLevel": {"Default": "Information", "UNI.Utilities.ExternalStorage": "Debug"}}}