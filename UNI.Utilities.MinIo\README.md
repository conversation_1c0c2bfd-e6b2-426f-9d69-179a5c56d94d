# UNI.Utilities.MinIo

A comprehensive .NET library for MinIO object storage operations, providing a clean and easy-to-use interface for all common MinIO operations.

## Features

- **Bucket Operations**: Create, delete, list, and check bucket existence
- **Object Operations**: Upload, download, delete, and get object information
- **Batch Operations**: Delete multiple objects at once
- **Presigned URLs**: Generate temporary URLs for upload and download
- **Comprehensive Logging**: Built-in logging support
- **Dependency Injection**: Easy integration with .NET DI container
- **Async/Await**: Full async support with cancellation tokens
- **Error Handling**: Custom exceptions with detailed error information

## Installation

Add the project reference to your application:

```xml
<ItemGroup>
  <ProjectReference Include="..\UNI.Utilities.MinIo\UNI.Utilities.MinIo.csproj" />
</ItemGroup>
```

## Configuration

### appsettings.json

#### Basic Configuration

```json
{
  "MinIo": {
    "Endpoint": "localhost:9000",
    "ProxyEndpoint": "https://your-proxy-domain.com",
    "AccessKey": "your-access-key",
    "SecretKey": "your-secret-key",
    "UseSSL": false,
    "Region": "us-east-1",
    "DefaultBucket": "my-bucket",
    "TimeoutSeconds": 30,
    "CreateBucketIfNotExists": true
  }
}
```

#### Proxy Endpoint Configuration

The `ProxyEndpoint` setting allows you to configure a public-facing URL that will be used in presigned URLs instead of the internal MinIO endpoint. This is useful when:

- MinIO is running behind a reverse proxy or load balancer
- You need to provide public access through a different domain
- Your MinIO server is in a private network but accessible via a proxy

**Example scenarios:**

1. **Internal MinIO with Public Proxy:**
   ```json
   {
     "MinIo": {
       "Endpoint": "internal-minio:9000",
       "ProxyEndpoint": "https://files.yourdomain.com",
       "AccessKey": "your-access-key",
       "SecretKey": "your-secret-key"
     }
   }
   ```

2. **Development vs Production:**
   ```json
   {
     "MinIo": {
       "Endpoint": "localhost:9000",
       "ProxyEndpoint": "https://cdn.yourapp.com",
       "AccessKey": "your-access-key",
       "SecretKey": "your-secret-key"
     }
   }
   ```

When `ProxyEndpoint` is configured:
- Direct operations (upload, download, delete) use the `Endpoint`
- Presigned URLs use the `ProxyEndpoint` for public accessibility
- If `ProxyEndpoint` is not set, presigned URLs use the `Endpoint`

### Dependency Injection Setup

```csharp
using UNI.Utilities.MinIo.Extensions;

public class Startup
{
    public void ConfigureServices(IServiceCollection services)
    {
        // Option 1: Using configuration section
        services.AddMinIoService(Configuration.GetSection("MinIo"));
        
        // Option 2: Using settings object
        services.AddMinIoService(new MinIoSettings
        {
            Endpoint = "localhost:9000",
            AccessKey = "your-access-key",
            SecretKey = "your-secret-key",
            UseSSL = false
        });
        
        // Option 3: Using configuration action
        services.AddMinIoService(settings =>
        {
            settings.Endpoint = "localhost:9000";
            settings.AccessKey = "your-access-key";
            settings.SecretKey = "your-secret-key";
            settings.UseSSL = false;
        });
    }
}
```

## Usage Examples

### Basic Operations

```csharp
public class FileService
{
    private readonly IMinIoService _minIoService;

    public FileService(IMinIoService minIoService)
    {
        _minIoService = minIoService;
    }

    // Upload a file
    public async Task<UploadResult> UploadFileAsync(string filePath, string bucketName, string objectName)
    {
        return await _minIoService.UploadFileAsync(bucketName, objectName, filePath);
    }

    // Upload from stream
    public async Task<UploadResult> UploadStreamAsync(Stream stream, string bucketName, string objectName)
    {
        return await _minIoService.UploadObjectAsync(bucketName, objectName, stream);
    }

    // Download to file
    public async Task DownloadFileAsync(string bucketName, string objectName, string filePath)
    {
        await _minIoService.DownloadFileAsync(bucketName, objectName, filePath);
    }

    // Download as byte array
    public async Task<byte[]> DownloadBytesAsync(string bucketName, string objectName)
    {
        return await _minIoService.GetObjectBytesAsync(bucketName, objectName);
    }

    // Check if object exists
    public async Task<bool> FileExistsAsync(string bucketName, string objectName)
    {
        return await _minIoService.ObjectExistsAsync(bucketName, objectName);
    }

    // Delete object
    public async Task DeleteFileAsync(string bucketName, string objectName)
    {
        await _minIoService.DeleteObjectAsync(bucketName, objectName);
    }
}
```

### Large File Upload

For large files, use the specialized large file upload methods that provide better performance, reliability, and progress tracking:

```csharp
// Upload large file with progress tracking
var progress = new Progress<UploadProgress>(p =>
{
    Console.WriteLine($"Upload Progress: {p.PercentageComplete:F1}% " +
                     $"({p.UploadedBytes:N0}/{p.TotalBytes:N0} bytes)");

    if (p.BytesPerSecond.HasValue)
        Console.WriteLine($"Speed: {p.BytesPerSecond.Value:N0} bytes/sec");

    if (p.EstimatedTimeRemaining.HasValue)
        Console.WriteLine($"ETA: {p.EstimatedTimeRemaining.Value:mm\\:ss}");
});

// Upload large file from disk
var result = await _minIoService.UploadLargeFileAsync(
    "my-bucket",
    "large-video.mp4",
    "/path/to/large-video.mp4",
    partSize: 64 * 1024 * 1024, // 64MB parts
    progressCallback: progress);

// Upload large object from stream
using var fileStream = File.OpenRead("/path/to/large-file.zip");
var result = await _minIoService.UploadLargeObjectAsync(
    "my-bucket",
    "large-archive.zip",
    fileStream,
    fileStream.Length,
    partSize: 32 * 1024 * 1024, // 32MB parts
    progressCallback: progress);
```

#### Large File Upload Features:

- **Multipart Upload**: Automatically splits large files into smaller parts
- **Progress Tracking**: Real-time progress reporting with speed and ETA
- **Configurable Part Size**: Customize part size (5MB - 5GB, default: 64MB)
- **Reliability**: Better handling of network interruptions
- **Performance**: Optimized for large file transfers

### Bucket Management

```csharp
// Create bucket
await _minIoService.CreateBucketAsync("my-bucket");

// Check if bucket exists
var exists = await _minIoService.BucketExistsAsync("my-bucket");

// List all buckets
var buckets = await _minIoService.ListBucketsAsync();

// Delete bucket
await _minIoService.DeleteBucketAsync("my-bucket");
```

### Object Listing and Information

```csharp
// List all objects in bucket
var objects = await _minIoService.ListObjectsAsync("my-bucket");

// List objects with prefix
var filteredObjects = await _minIoService.ListObjectsAsync("my-bucket", "documents/");

// List objects recursively
var allObjects = await _minIoService.ListObjectsAsync("my-bucket", recursive: true);

// Get object information
var objectInfo = await _minIoService.GetObjectInfoAsync("my-bucket", "my-file.pdf");
```

### Presigned URLs

```csharp
// Generate download URL (valid for 7 days by default)
var downloadUrl = await _minIoService.GetPresignedDownloadUrlAsync("my-bucket", "my-file.pdf");

// Generate preview URL for inline viewing (images, PDFs, etc.)
var previewUrl = await _minIoService.GetPreviewUrlAsync("my-bucket", "image.jpg");

// Generate upload URL with custom expiry
var uploadUrl = await _minIoService.GetPresignedUploadUrlAsync(
    "my-bucket",
    "new-file.pdf",
    TimeSpan.FromHours(1));
```

#### Preview vs Download URLs

The library provides two types of URLs for accessing objects:

**Download URLs** (`GetPresignedDownloadUrlAsync`):
- Forces the browser to download the file
- Sets `Content-Disposition: attachment` header
- Best for: File downloads, document downloads

**Preview URLs** (`GetPreviewUrlAsync`):
- Displays the file inline in the browser
- Sets `Content-Disposition: inline` header
- Automatically detects and sets appropriate content-type
- Best for: Image galleries, PDF viewers, document previews

```csharp
// For downloading files
var downloadUrl = await _minIoService.GetPresignedDownloadUrlAsync("bucket", "document.pdf");

// For previewing/displaying files in browser
var previewUrl = await _minIoService.GetPreviewUrlAsync("bucket", "image.jpg");
```

### Batch Operations

```csharp
// Delete multiple objects
var objectsToDelete = new[] { "file1.pdf", "file2.pdf", "file3.pdf" };
var deleteResults = await _minIoService.DeleteObjectsAsync("my-bucket", objectsToDelete);

foreach (var result in deleteResults)
{
    if (result.IsSuccess)
        Console.WriteLine($"Successfully deleted {result.ObjectName}");
    else
        Console.WriteLine($"Failed to delete {result.ObjectName}: {result.ErrorMessage}");
}
```

### Error Handling

```csharp
try
{
    await _minIoService.UploadFileAsync("my-bucket", "my-file.pdf", "/path/to/file.pdf");
}
catch (MinIoException ex)
{
    Console.WriteLine($"MinIO Error: {ex.Message}");
    Console.WriteLine($"Error Code: {ex.ErrorCode}");
    Console.WriteLine($"Bucket: {ex.BucketName}");
    Console.WriteLine($"Object: {ex.ObjectName}");
}
catch (FileNotFoundException ex)
{
    Console.WriteLine($"File not found: {ex.Message}");
}
```

## API Reference

### IMinIoService Interface

The main interface provides the following method categories:

- **Bucket Operations**: `BucketExistsAsync`, `CreateBucketAsync`, `DeleteBucketAsync`, `ListBucketsAsync`
- **Object Operations**: `UploadObjectAsync`, `UploadFileAsync`, `UploadLargeFileAsync`, `UploadLargeObjectAsync`, `DownloadObjectAsync`, `DownloadFileAsync`, `GetObjectBytesAsync`, `ObjectExistsAsync`, `DeleteObjectAsync`, `DeleteObjectsAsync`, `GetObjectInfoAsync`, `ListObjectsAsync`
- **URL Operations**: `GetPresignedDownloadUrlAsync`, `GetPresignedUploadUrlAsync`, `GetPreviewUrlAsync`

### Configuration Properties

#### MinIoSettings

| Property | Type | Default | Description |
|----------|------|---------|-------------|
| `Endpoint` | `string` | `""` | MinIO server endpoint (required) |
| `ProxyEndpoint` | `string?` | `null` | Public proxy endpoint for presigned URLs (optional) |
| `AccessKey` | `string` | `""` | Access key for authentication (required) |
| `SecretKey` | `string` | `""` | Secret key for authentication (required) |
| `UseSSL` | `bool` | `true` | Whether to use SSL/TLS |
| `Region` | `string?` | `null` | MinIO server region (optional) |
| `DefaultBucket` | `string?` | `null` | Default bucket name (optional) |
| `TimeoutSeconds` | `int` | `30` | Connection timeout in seconds |
| `CreateBucketIfNotExists` | `bool` | `false` | Auto-create buckets if they don't exist |

### Models

- **BucketInfo**: Information about a bucket
- **ObjectInfo**: Information about an object
- **UploadResult**: Result of an upload operation (includes multipart upload info)
- **UploadProgress**: Progress information for large file uploads
- **DeleteResult**: Result of a delete operation
- **MinIoSettings**: Configuration settings
- **MinIoException**: Custom exception for MinIO operations

## License

This project is part of the UNI.Utilities suite.
