using Moq;
using UNI.Utilities.ExternalStorage.Abstractions;
using UNI.Utilities.ExternalStorage.Models;
using UNI.Utilities.ExternalStorage.Services;
using Xunit;

namespace UNI.Utilities.ExternalStorage.Tests.Unit
{
    public class StorageProviderRegistryTests
    {
        private readonly StorageProviderRegistry _registry;

        public StorageProviderRegistryTests()
        {
            _registry = new StorageProviderRegistry();
        }

        [Fact]
        public void RegisterProvider_WithValidParameters_ShouldRegisterProvider()
        {
            // Arrange
            var mockStorageService = new Mock<IStorageService>();
            Func<IStorageService> factory = () => mockStorageService.Object;

            // Act
            _registry.RegisterProvider(StorageProviderType.AzureBlob, factory);

            // Assert
            var retrievedFactory = _registry.GetProviderFactory(StorageProviderType.AzureBlob);
            Assert.NotNull(retrievedFactory);
            Assert.Same(mockStorageService.Object, retrievedFactory());
        }

        [Fact]
        public void RegisterProvider_WithNullFactory_ShouldThrowArgumentNullException()
        {
            // Act & Assert
            Assert.Throws<ArgumentNullException>(() =>
                _registry.RegisterProvider(StorageProviderType.AzureBlob, null!));
        }

        [Fact]
        public void RegisterProvider_WithSameProviderTypeTwice_ShouldOverwriteFirstRegistration()
        {
            // Arrange
            var mockStorageService1 = new Mock<IStorageService>();
            var mockStorageService2 = new Mock<IStorageService>();
            Func<IStorageService> factory1 = () => mockStorageService1.Object;
            Func<IStorageService> factory2 = () => mockStorageService2.Object;

            // Act
            _registry.RegisterProvider(StorageProviderType.AzureBlob, factory1);
            _registry.RegisterProvider(StorageProviderType.AzureBlob, factory2);

            // Assert
            var retrievedFactory = _registry.GetProviderFactory(StorageProviderType.AzureBlob);
            Assert.NotNull(retrievedFactory);
            Assert.Same(mockStorageService2.Object, retrievedFactory());
        }

        [Fact]
        public void UnregisterProvider_WithRegisteredProvider_ShouldRemoveProvider()
        {
            // Arrange
            var mockStorageService = new Mock<IStorageService>();
            Func<IStorageService> factory = () => mockStorageService.Object;
            _registry.RegisterProvider(StorageProviderType.AzureBlob, factory);

            // Act
            _registry.UnregisterProvider(StorageProviderType.AzureBlob);

            // Assert
            var retrievedFactory = _registry.GetProviderFactory(StorageProviderType.AzureBlob);
            Assert.Null(retrievedFactory);
        }

        [Fact]
        public void UnregisterProvider_WithUnregisteredProvider_ShouldNotThrow()
        {
            // Act & Assert (should not throw)
            _registry.UnregisterProvider(StorageProviderType.AzureBlob);
        }

        [Fact]
        public void GetProviderFactory_WithUnregisteredProvider_ShouldReturnNull()
        {
            // Act
            var factory = _registry.GetProviderFactory(StorageProviderType.AzureBlob);

            // Assert
            Assert.Null(factory);
        }

        [Fact]
        public void GetRegisteredProviders_WithNoRegistrations_ShouldReturnEmptyCollection()
        {
            // Act
            var providers = _registry.GetRegisteredProviders();

            // Assert
            Assert.Empty(providers);
        }

        [Fact]
        public void GetRegisteredProviders_WithMultipleRegistrations_ShouldReturnAllProviderTypes()
        {
            // Arrange
            var mockStorageService1 = new Mock<IStorageService>();
            var mockStorageService2 = new Mock<IStorageService>();
            Func<IStorageService> factory1 = () => mockStorageService1.Object;
            Func<IStorageService> factory2 = () => mockStorageService2.Object;

            _registry.RegisterProvider(StorageProviderType.AzureBlob, factory1);
            _registry.RegisterProvider(StorageProviderType.GoogleCloud, factory2);

            // Act
            var providers = _registry.GetRegisteredProviders().ToList();

            // Assert
            Assert.Equal(2, providers.Count);
            Assert.Contains(StorageProviderType.AzureBlob, providers);
            Assert.Contains(StorageProviderType.GoogleCloud, providers);
        }

        [Fact]
        public async Task Registry_ShouldBeThreadSafe()
        {
            // Arrange
            var tasks = new List<Task>();
            var mockStorageService = new Mock<IStorageService>();
            Func<IStorageService> factory = () => mockStorageService.Object;

            // Act - Register and unregister providers concurrently
            for (int i = 0; i < 10; i++)
            {
                var providerType = (StorageProviderType)(i % 5); // Use different provider types
                tasks.Add(Task.Run(() => _registry.RegisterProvider(providerType, factory)));
                tasks.Add(Task.Run(() => _registry.UnregisterProvider(providerType)));
                tasks.Add(Task.Run(() => _registry.GetProviderFactory(providerType)));
                tasks.Add(Task.Run(() => _registry.GetRegisteredProviders()));
            }
            await Task.WhenAll(tasks.ToArray()).WaitAsync(TimeSpan.FromSeconds(5));
            // Assert - Should not throw any exceptions
            Assert.All(tasks, t => Assert.True(t.IsCompletedSuccessfully));
        }

        [Fact]
        public void RegisterProvider_WithMultipleProviders_ShouldMaintainSeparateRegistrations()
        {
            // Arrange
            var mockStorageService1 = new Mock<IStorageService>();
            var mockStorageService2 = new Mock<IStorageService>();
            var mockStorageService3 = new Mock<IStorageService>();
            
            Func<IStorageService> factory1 = () => mockStorageService1.Object;
            Func<IStorageService> factory2 = () => mockStorageService2.Object;
            Func<IStorageService> factory3 = () => mockStorageService3.Object;

            // Act
            _registry.RegisterProvider(StorageProviderType.AzureBlob, factory1);
            _registry.RegisterProvider(StorageProviderType.GoogleCloud, factory2);
            _registry.RegisterProvider(StorageProviderType.MinIO, factory3);

            // Assert
            var factory1Retrieved = _registry.GetProviderFactory(StorageProviderType.AzureBlob);
            var factory2Retrieved = _registry.GetProviderFactory(StorageProviderType.GoogleCloud);
            var factory3Retrieved = _registry.GetProviderFactory(StorageProviderType.MinIO);

            Assert.NotNull(factory1Retrieved);
            Assert.NotNull(factory2Retrieved);
            Assert.NotNull(factory3Retrieved);

            Assert.Same(mockStorageService1.Object, factory1Retrieved());
            Assert.Same(mockStorageService2.Object, factory2Retrieved());
            Assert.Same(mockStorageService3.Object, factory3Retrieved());
        }
    }
}
