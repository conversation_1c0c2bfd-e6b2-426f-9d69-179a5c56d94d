﻿
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Text;
using UNI.Model.Api;

namespace UNI.Model
{
    public class viewBasePage : viewBasePage<object>
    {

    }

    public class viewBasePage<T>
    {
        public List<viewGridFlex> gridflexs { get; set; }
        public ResponseList<List<T>> dataList { get; set; }
    }
    public class viewBasePageT<T>
    {
        public List<viewGridFlex> gridflexs { get; set; }
        public ResponseList<T> dataList { get; set; }
    }
    public class viewDataPage : viewDataPage<object>
    {

    }
    public class viewDataPage<T>
    {
        public long recordsTotal { get; set; }
        public long recordsFiltered { get; set; }
        public string gridKey { get; set; }
        public int gridType { get; set; }
        public List<viewGridFlex> gridflexs { get; set; }
        public List<T> dataList { get; set; }
    }
    public class CommonOStatus
    {
        public Guid Oid { get; set; }
        public int status { get; set; }
        public string comment { get; set; }
    }
    public class CommonStatus
    {
        public Guid gd { get; set; }
        public int status { get; set; }
        public string comment { get; set; }
    }
    public class CommonId
    {
        public Guid? id { get; set; }
    }
    public class CommonOid
    {
        public Guid? Oid { get; set; }
    }
    public class CommonGd
    {
        public Guid gd { get; set; }
    }
    public class CommonStatuses
    {
        public List<CommonGd> gIds { get; set; }
        public int status { get; set; }
        public string comment { get; set; }
    }
    public class CommonLabel
    {
        public string label { get; set; }
        public decimal value { get; set; }
    }
    public class CommonValue
    {
        public string name { get; set; }
        public string value { get; set; }
        public bool icon_is { get; set; }
        public string icon { get; set; }
        public bool isHtml { get; set; }
    }
    public class CommonInput
    {
        public string tableName { get; set; } // Tên bảng cần lấy
        public string columnKey { get; set; } // Cột key cần lấy
        public string columnName { get; set; } // Cột cần lấy
        public string filter { get; set; } // Giá trị cần tìm kiếm
        public string Oid { get; set; } // Id
        public string ColumnDisplay { get; set; } // Cột "cha" cần kiểm tra
        public string OidDisplay { get; set; } // Giá trị "cha" kiểm tra
        public string all { get; set; } // Giá trị trường "tất cả"
        public string columnIcon { get; set; }  // Cột lưu url icon
        public string columnMax { get; set; }  // giá trị max cần lấy
        public string columnEqual { get; set; }  // cột lưu giá trị
        public string equalValue { get; set; }  // giá trị tương ứng
        public string orderby { get; set; }  // giá trị tương ứng

    }
    public class CommonManageList
    {
        public string filter { get; set; } // Giá trị cần tìm kiếm
        public string Oid { get; set; } // Id
        public string all { get; set; } // Giá trị trường "tất cả"
       // public string Department { get; set; } // Phòng ban cần lấy quản lý
        public string TableDisplay { get; set; } // Bảng "cha" cần kiểm tra
        public string OidDisplay { get; set; } // Giá trị "cha" kiểm tra

    }
    public class CommonViewInfo : viewBaseInfo
    {
        public long? id { get; set; }
        public Guid? gd { get; set; }
        public string cd { get; set; }
        //dùng cho những form có y/c confirm trước khi thao tác
        public bool Accept { get; set; }
    }
    public class CommonViewSelectTime : CommonViewInfo
    {
        //Them vao 11.3.2024 (Select Quarter, Month, Year)
        public string Parameter { get; set; }
    }
    public class CommonViewIdInfo : viewBaseInfo
    {
        public Guid? id { get; set; }
    }
    public class CommonViewOidInfo : viewBaseInfo
    {
        public Guid? Oid { get; set; }
    }

    public class CommonViewOrdOidInfo : viewBaseInfo
    {
        public Guid? ordOid { get; set;  }
    }

    public class CommonListPage : viewBasePage<object>
    {
    }
    public class CommonResponseList : ResponseList<object>
    {
        public CommonResponseList()
        {

        }
        public CommonResponseList(ApiResult status) : base(status) { }
        public CommonResponseList(ApiResult status, string error) : base(status, error) { }

        public CommonResponseList(ApiResult status, object data) : base(status, data) { }
        public CommonResponseList(object data, long recordsTotal, long recordsFiltered) : base(data, recordsTotal, recordsFiltered)
        {
        }
    }
    public class CommonDataPage : viewDataPage<object>
    {
        public Guid? sourceId { get; set; }
    }
    public class BaseImportSet<T>
    {
        public bool accept { get; set; }
        public List<T> imports { get; set; }
        public uImportFile importFile { get; set; }

        public BaseImportSet()
        {
            this.imports = new List<T>();
        }
    }

    public class TableFK
    {
        public string ColumnName { get; set; }
        public string ReferencedTable { get; set; }
        public string ReferencedColumn { get; set; }
    }
    public class ImportListPage : viewDataPage<object>
    {
        public bool valid { get; set; }
        public string messages { get; set; }
        public bool accept { get; set; }
        public long recordsFail { get; set; }
        public long recordsAccepted { get; set; }
        public Guid? Oid { get; set; }
        public uImportFile importFile { get; set; }
    }
    public class uImportFile
    {
        public Guid? impId { get; set; }
        public string fileName { get; set; }
        public string fileType { get; set; }
        public long fileSize { get; set; }
        public string fileUrl { get; set; }
    }
    public class FilterInpTableKey : FilterInput
    {
        public string tableKey { get; set; }
        public string LanguageName { get; set; }
    }
    public class FilterInpGridKey : FilterInput
    {
        public string gridKey { get; set; }
        public string LanguageName { get; set; }
    }
    public class ConfigFormPage : viewBasePage<ConfigField>
    {

    }
    public class ConfigFormPage<T> : viewBasePage<T>
    {

    }
    public class ConfigField : viewField
    {
        public long id { get; set; }
        public int view_type { get; set; }
        public string ordinal { get; set; }
        public string columnDefault { get; set; }
        public bool isPrivate { get; set; }
    }
    public class ConfigListPage : viewBasePage<ConfigColumn>
    {

    }
    public class ConfigColumn
    {
        public long id { get; set; }
        public string view_grid { get; set; }
        public int view_type { get; set; }
        public string data_type { get; set; }
        public int ordinal { get; set; }
        public bool isUsed { get; set; }
        public string cellClass { get; set; }
        public string columnField { get; set; }
        public string columnCaption { get; set; }
        public string columnCaptionE { get; set; }
        public int columnWidth { get; set; }
        public string fieldType { get; set; }
        public string pinned { get; set; }
        public bool isMasterDetail { get; set; }
        public bool isStatusLable { get; set; }
        public bool isHide { get; set; }
        public bool isFilter { get; set; }
        public string columnObject { get; set; }
        public Guid? customOid { get; set; }
    }
}
