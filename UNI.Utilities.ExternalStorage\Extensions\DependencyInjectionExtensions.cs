using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using UNI.Utilities.ExternalStorage.Abstractions;
using UNI.Utilities.ExternalStorage.Models;
using UNI.Utilities.ExternalStorage.Providers;
using UNI.Utilities.ExternalStorage.Services;

namespace UNI.Utilities.ExternalStorage.Extensions
{
    /// <summary>
    /// Extension methods for dependency injection
    /// </summary>
    public static class DependencyInjectionExtensions
    {
        /// <summary>
        /// Add external storage services to the service collection
        /// </summary>
        /// <param name="services">Service collection</param>
        /// <param name="configurationSection">Configuration section containing external storage settings</param>
        /// <returns>Service collection for chaining</returns>
        public static IServiceCollection AddExternalStorageServices(
            this IServiceCollection services,
            IConfigurationSection configurationSection)
        {
            services.Configure<ExternalStorageConfiguration>(configurationSection);
            return AddExternalStorageServices(services);
        }

        /// <summary>
        /// Add external storage services to the service collection with configuration
        /// </summary>
        /// <param name="services">Service collection</param>
        /// <param name="configuration">External storage configuration</param>
        /// <returns>Service collection for chaining</returns>
        public static IServiceCollection AddExternalStorageServices(
            this IServiceCollection services,
            ExternalStorageConfiguration configuration)
        {
            services.AddSingleton(configuration);
            services.Configure<ExternalStorageConfiguration>(config =>
            {
                config.DefaultProvider = configuration.DefaultProvider;
                config.MinIO = configuration.MinIO;
                config.AwsS3 = configuration.AwsS3;
                config.Firebase = configuration.Firebase;
                config.EnableFallback = configuration.EnableFallback;
                config.FallbackOrder = configuration.FallbackOrder;
            });
            return AddExternalStorageServices(services);
        }

        /// <summary>
        /// Add external storage services to the service collection with configuration action
        /// </summary>
        /// <param name="services">Service collection</param>
        /// <param name="configureOptions">Action to configure external storage settings</param>
        /// <returns>Service collection for chaining</returns>
        public static IServiceCollection AddExternalStorageServices(
            this IServiceCollection services,
            Action<ExternalStorageConfiguration> configureOptions)
        {
            services.Configure(configureOptions);
            return AddExternalStorageServices(services);
        }

        /// <summary>
        /// Add external storage services to the service collection
        /// </summary>
        /// <param name="services">Service collection</param>
        /// <returns>Service collection for chaining</returns>
        private static IServiceCollection AddExternalStorageServices(IServiceCollection services)
        {
            // Register core services
            services.AddSingleton<IStorageProviderRegistry, StorageProviderRegistry>();
            services.AddSingleton<IStorageServiceFactory, StorageServiceFactory>();

            // Register default storage service as singleton
            services.AddSingleton<IStorageService>(provider =>
            {
                var factory = provider.GetRequiredService<IStorageServiceFactory>();
                return factory.CreateDefaultStorageService();
            });

            // Add logging
            services.AddLogging();

            return services;
        }

        /// <summary>
        /// Add MinIO storage provider to the service collection
        /// </summary>
        /// <param name="services">Service collection</param>
        /// <param name="configurationSection">Configuration section containing MinIO settings</param>
        /// <returns>Service collection for chaining</returns>
        public static IServiceCollection AddMinIOStorageProvider(
            this IServiceCollection services,
            IConfigurationSection configurationSection)
        {
            services.Configure<MinIOStorageSettings>(configurationSection);
            services.AddScoped<MinIOStorageProvider>(provider =>
            {
                var settings = provider.GetRequiredService<IConfiguration>()
                    .GetSection(configurationSection.Path)
                    .Get<MinIOStorageSettings>() ?? throw new InvalidOperationException("MinIO settings not configured");
                var logger = provider.GetRequiredService<ILogger<MinIOStorageProvider>>();
                return new MinIOStorageProvider(settings, logger);
            });
            return services;
        }

        /// <summary>
        /// Add MinIO storage provider to the service collection with settings
        /// </summary>
        /// <param name="services">Service collection</param>
        /// <param name="settings">MinIO storage settings</param>
        /// <returns>Service collection for chaining</returns>
        public static IServiceCollection AddMinIOStorageProvider(
            this IServiceCollection services,
            MinIOStorageSettings settings)
        {
            services.AddSingleton(settings);
            services.AddScoped<MinIOStorageProvider>(provider =>
            {
                var logger = provider.GetRequiredService<ILogger<MinIOStorageProvider>>();
                return new MinIOStorageProvider(settings, logger);
            });
            return services;
        }

        /// <summary>
        /// Add AWS S3 storage provider to the service collection
        /// </summary>
        /// <param name="services">Service collection</param>
        /// <param name="configurationSection">Configuration section containing AWS S3 settings</param>
        /// <returns>Service collection for chaining</returns>
        public static IServiceCollection AddAwsS3StorageProvider(
            this IServiceCollection services,
            IConfigurationSection configurationSection)
        {
            services.Configure<AwsS3StorageSettings>(configurationSection);
            services.AddScoped<AwsS3StorageProvider>(provider =>
            {
                var settings = provider.GetRequiredService<IConfiguration>()
                    .GetSection(configurationSection.Path)
                    .Get<AwsS3StorageSettings>() ?? throw new InvalidOperationException("AWS S3 settings not configured");
                var logger = provider.GetRequiredService<ILogger<AwsS3StorageProvider>>();
                return new AwsS3StorageProvider(settings, logger);
            });
            return services;
        }

        /// <summary>
        /// Add AWS S3 storage provider to the service collection with settings
        /// </summary>
        /// <param name="services">Service collection</param>
        /// <param name="settings">AWS S3 storage settings</param>
        /// <returns>Service collection for chaining</returns>
        public static IServiceCollection AddAwsS3StorageProvider(
            this IServiceCollection services,
            AwsS3StorageSettings settings)
        {
            services.AddSingleton(settings);
            services.AddScoped<AwsS3StorageProvider>(provider =>
            {
                var logger = provider.GetRequiredService<ILogger<AwsS3StorageProvider>>();
                return new AwsS3StorageProvider(settings, logger);
            });
            return services;
        }

        /*
        /// <summary>
        /// Add Firebase storage provider to the service collection
        /// </summary>
        /// <param name="services">Service collection</param>
        /// <param name="configurationSection">Configuration section containing Firebase settings</param>
        /// <returns>Service collection for chaining</returns>
        public static IServiceCollection AddFirebaseStorageProvider(
            this IServiceCollection services,
            IConfigurationSection configurationSection)
        {
            services.Configure<FirebaseStorageSettings>(configurationSection);
            services.AddScoped<FirebaseStorageProvider>(provider =>
            {
                var settings = provider.GetRequiredService<IConfiguration>()
                    .GetSection(configurationSection.Path)
                    .Get<FirebaseStorageSettings>() ?? throw new InvalidOperationException("Firebase settings not configured");
                var logger = provider.GetRequiredService<ILogger<FirebaseStorageProvider>>();
                return new FirebaseStorageProvider(settings, logger);
            });
            return services;
        }

        /// <summary>
        /// Add Firebase storage provider to the service collection with settings
        /// </summary>
        /// <param name="services">Service collection</param>
        /// <param name="settings">Firebase storage settings</param>
        /// <returns>Service collection for chaining</returns>
        public static IServiceCollection AddFirebaseStorageProvider(
            this IServiceCollection services,
            FirebaseStorageSettings settings)
        {
            services.AddSingleton(settings);
            services.AddScoped<FirebaseStorageProvider>(provider =>
            {
                var logger = provider.GetRequiredService<ILogger<FirebaseStorageProvider>>();
                return new FirebaseStorageProvider(settings, logger);
            });
            return services;
        }
        */

        /// <summary>
        /// Register a custom storage provider
        /// </summary>
        /// <param name="services">Service collection</param>
        /// <param name="providerType">Provider type</param>
        /// <param name="factory">Factory function to create the provider</param>
        /// <returns>Service collection for chaining</returns>
        public static IServiceCollection RegisterStorageProvider(
            this IServiceCollection services,
            StorageProviderType providerType,
            Func<IServiceProvider, IStorageService> factory)
        {
            services.AddScoped(provider =>
            {
                var registry = provider.GetRequiredService<IStorageProviderRegistry>();
                registry.RegisterProvider(providerType, () => factory(provider));
                return registry;
            });
            return services;
        }

        /// <summary>
        /// Add storage service with fallback support
        /// </summary>
        /// <param name="services">Service collection</param>
        /// <param name="primaryProvider">Primary storage provider type</param>
        /// <param name="fallbackProviders">Fallback provider types in order</param>
        /// <returns>Service collection for chaining</returns>
        public static IServiceCollection AddStorageServiceWithFallback(
            this IServiceCollection services,
            StorageProviderType primaryProvider,
            params StorageProviderType[] fallbackProviders)
        {
            services.Configure<ExternalStorageConfiguration>(config =>
            {
                config.DefaultProvider = primaryProvider;
                config.EnableFallback = true;
                config.FallbackOrder = fallbackProviders.ToList();
            });

            return services;
        }
    }
}
