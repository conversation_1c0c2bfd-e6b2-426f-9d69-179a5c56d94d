{"ExternalStorage": {"DefaultProvider": "MinIO", "EnableFallback": false, "FallbackOrder": ["AwsS3", "Firebase"], "MinIO": {"Endpoint": "storage-dev.unicloudgroup.com.vn", "ProxyEndpoint": "https://test-proxy.example.com", "AccessKey": "iZJWUfthMR4McKt6VvBO", "SecretKey": "jUyIENNflJeHzMS4SH9qh5nWR7b6h47DMLaLiXcE", "UseSSL": true, "Region": "", "DefaultBucket": "test-bucket", "TimeoutSeconds": 30, "CreateBucketIfNotExists": true}, "AwsS3": {"AccessKeyId": "test-aws-access-key-id", "SecretAccessKey": "test-aws-secret-access-key", "Region": "us-east-1", "DefaultBucket": "test-s3-bucket", "TimeoutSeconds": 30, "CreateBucketIfNotExists": true}, "Firebase": {"ProjectId": "test-firebase-project", "ServiceAccountKeyJson": "{\"type\":\"service_account\",\"project_id\":\"test-firebase-project\"}", "StorageBucket": "test-firebase-project.appspot.com", "DefaultBucket": "test-firebase-bucket", "TimeoutSeconds": 30, "CreateBucketIfNotExists": true}}, "Logging": {"LogLevel": {"Default": "Information", "UNI.Utilities.ExternalStorage": "Debug"}}}