using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Options;
using UNI.Utilities.ExternalStorage.Abstractions;
using UNI.Utilities.ExternalStorage.Extensions;
using UNI.Utilities.ExternalStorage.Models;
using UNI.Utilities.ExternalStorage.Providers;
using Xunit;

namespace UNI.Utilities.ExternalStorage.Tests.Unit
{
    public class DependencyInjectionExtensionsTests
    {
        [Fact]
        public void AddExternalStorageServices_WithConfigurationSection_ShouldRegisterServices()
        {
            // Arrange
            var services = new ServiceCollection();
            var configuration = new ConfigurationBuilder()
                .AddJsonFile("appsettings.test.json", optional: true)
                .Build();

            var configSection = configuration.GetSection("ExternalStorage");

            // Act
            services.AddExternalStorageServices(configSection);

            // Assert
            var serviceProvider = services.BuildServiceProvider();
            
            Assert.NotNull(serviceProvider.GetService<IStorageServiceFactory>());
            Assert.NotNull(serviceProvider.GetService<IStorageProviderRegistry>());
            Assert.NotNull(serviceProvider.GetService<IStorageService>());
        }

        [Fact]
        public void AddExternalStorageServices_WithConfiguration_ShouldRegisterServices()
        {
            // Arrange
            var services = new ServiceCollection();
            var configuration = new ExternalStorageConfiguration
            {
                DefaultProvider = StorageProviderType.MinIO,
                MinIO = new MinIOStorageSettings
                {
                    Endpoint = "localhost:9000",
                    AccessKey = "test-access-key",
                    SecretKey = "test-secret-key",
                    UseSSL = false
                }
            };

            // Act
            services.AddExternalStorageServices(configuration);

            // Assert
            var serviceProvider = services.BuildServiceProvider();
            
            Assert.NotNull(serviceProvider.GetService<IStorageServiceFactory>());
            Assert.NotNull(serviceProvider.GetService<IStorageProviderRegistry>());
            Assert.NotNull(serviceProvider.GetService<IStorageService>());

            var options = serviceProvider.GetService<IOptions<ExternalStorageConfiguration>>();
            Assert.NotNull(options);
            Assert.Equal(StorageProviderType.MinIO, options.Value.DefaultProvider);
        }

        [Fact]
        public void AddExternalStorageServices_WithConfigureAction_ShouldRegisterServices()
        {
            // Arrange
            var services = new ServiceCollection();

            // Act
            services.AddExternalStorageServices(config =>
            {
                config.DefaultProvider = StorageProviderType.AwsS3;
                config.AwsS3 = new AwsS3StorageSettings
                {
                    AccessKeyId = "test-access-key-id",
                    SecretAccessKey = "test-secret-access-key",
                    Region = "us-east-1"
                };
            });

            // Assert
            var serviceProvider = services.BuildServiceProvider();
            
            Assert.NotNull(serviceProvider.GetService<IStorageServiceFactory>());
            Assert.NotNull(serviceProvider.GetService<IStorageProviderRegistry>());
            Assert.NotNull(serviceProvider.GetService<IStorageService>());

            var options = serviceProvider.GetService<IOptions<ExternalStorageConfiguration>>();
            Assert.NotNull(options);
            Assert.Equal(StorageProviderType.AwsS3, options.Value.DefaultProvider);
        }

        [Fact]
        public void AddMinIOStorageProvider_WithSettings_ShouldRegisterProvider()
        {
            // Arrange
            var services = new ServiceCollection();
            services.AddLogging();

            var settings = new MinIOStorageSettings
            {
                Endpoint = "localhost:9000",
                AccessKey = "test-access-key",
                SecretKey = "test-secret-key",
                UseSSL = false
            };

            // Act
            services.AddMinIOStorageProvider(settings);

            // Assert
            var serviceProvider = services.BuildServiceProvider();
            var provider = serviceProvider.GetService<MinIOStorageProvider>();
            
            Assert.NotNull(provider);
            Assert.Equal(StorageProviderType.MinIO, provider.ProviderType);
        }

        [Fact]
        public void AddAwsS3StorageProvider_WithSettings_ShouldRegisterProvider()
        {
            // Arrange
            var services = new ServiceCollection();
            services.AddLogging();

            var settings = new AwsS3StorageSettings
            {
                AccessKeyId = "test-access-key-id",
                SecretAccessKey = "test-secret-access-key",
                Region = "us-east-1"
            };

            // Act
            services.AddAwsS3StorageProvider(settings);

            // Assert
            var serviceProvider = services.BuildServiceProvider();
            var provider = serviceProvider.GetService<AwsS3StorageProvider>();
            
            Assert.NotNull(provider);
            Assert.Equal(StorageProviderType.AwsS3, provider.ProviderType);
        }

        [Fact]
        public void AddFirebaseStorageProvider_WithSettings_ShouldRegisterProvider()
        {
            // Arrange
            var services = new ServiceCollection();
            services.AddLogging();

            var settings = new FirebaseStorageSettings
            {
                ProjectId = "test-project-id",
                ServiceAccountKeyJson = "{\"type\":\"service_account\",\"project_id\":\"test-project-id\"}"
            };

            // Act
            // Firebase provider is currently disabled
            // services.AddFirebaseStorageProvider(settings);

            // Assert
            // var serviceProvider = services.BuildServiceProvider();
            // var provider = serviceProvider.GetService<FirebaseStorageProvider>();

            // Assert.NotNull(provider);
            // Assert.Equal(StorageProviderType.Firebase, provider.ProviderType);

            // Skip this test since Firebase is disabled
            Assert.True(true);
        }

        [Fact]
        public void AddStorageServiceWithFallback_ShouldConfigureFallback()
        {
            // Arrange
            var services = new ServiceCollection();

            // Act
            services.AddStorageServiceWithFallback(
                StorageProviderType.MinIO,
                StorageProviderType.AwsS3,
                StorageProviderType.Firebase);

            // Assert
            var serviceProvider = services.BuildServiceProvider();
            var options = serviceProvider.GetService<IOptions<ExternalStorageConfiguration>>();
            
            Assert.NotNull(options);
            Assert.Equal(StorageProviderType.MinIO, options.Value.DefaultProvider);
            Assert.True(options.Value.EnableFallback);
            Assert.Equal(2, options.Value.FallbackOrder.Count);
            Assert.Contains(StorageProviderType.AwsS3, options.Value.FallbackOrder);
            Assert.Contains(StorageProviderType.Firebase, options.Value.FallbackOrder);
        }

        [Fact]
        public void RegisterStorageProvider_WithCustomProvider_ShouldRegisterInRegistry()
        {
            // Arrange
            var services = new ServiceCollection();
            services.AddExternalStorageServices(config =>
            {
                config.DefaultProvider = StorageProviderType.MinIO;
            });

            // Act
            services.RegisterStorageProvider(StorageProviderType.AzureBlob, provider =>
            {
                // Return a mock storage service for testing
                return new TestStorageService();
            });

            // Assert
            var serviceProvider = services.BuildServiceProvider();
            var registry = serviceProvider.GetService<IStorageProviderRegistry>();
            
            Assert.NotNull(registry);
            var factory = registry.GetProviderFactory(StorageProviderType.AzureBlob);
            Assert.NotNull(factory);
            
            var storageService = factory();
            Assert.NotNull(storageService);
            Assert.IsType<TestStorageService>(storageService);
        }

        [Fact]
        public void ServiceRegistration_ShouldUseScopedLifetime()
        {
            // Arrange
            var services = new ServiceCollection();
            services.AddExternalStorageServices(config =>
            {
                config.DefaultProvider = StorageProviderType.MinIO;
                config.MinIO = new MinIOStorageSettings
                {
                    Endpoint = "localhost:9000",
                    AccessKey = "test-access-key",
                    SecretKey = "test-secret-key",
                    UseSSL = false
                };
            });

            // Act
            var serviceProvider = services.BuildServiceProvider();

            // Assert
            using (var scope1 = serviceProvider.CreateScope())
            using (var scope2 = serviceProvider.CreateScope())
            {
                var service1a = scope1.ServiceProvider.GetService<IStorageService>();
                var service1b = scope1.ServiceProvider.GetService<IStorageService>();
                var service2 = scope2.ServiceProvider.GetService<IStorageService>();

                // Same instance within the same scope
                Assert.Same(service1a, service1b);
                
                // Different instances across different scopes
                Assert.NotSame(service1a, service2);
            }
        }

        // Test storage service implementation for testing purposes
        private class TestStorageService : IStorageService
        {
            public StorageProviderType ProviderType => StorageProviderType.AzureBlob;
            public string ProviderName => "Test Storage";

            public Task<bool> BucketExistsAsync(string? bucketName = null, CancellationToken cancellationToken = default) => Task.FromResult(true);
            public Task CreateBucketAsync(string? bucketName = null, string? region = null, CancellationToken cancellationToken = default) => Task.CompletedTask;
            public Task DeleteBucketAsync(string? bucketName = null, CancellationToken cancellationToken = default) => Task.CompletedTask;
            public Task<IEnumerable<BucketInfo>> ListBucketsAsync(CancellationToken cancellationToken = default) => Task.FromResult(Enumerable.Empty<BucketInfo>());
            public Task<StorageUploadResult> UploadObjectAsync(string objectName, Stream stream, string? bucketName = null, long? size = null, string? contentType = null, Dictionary<string, string>? metadata = null, CancellationToken cancellationToken = default) => Task.FromResult(new StorageUploadResult());
            public Task<StorageUploadResult> UploadFileAsync(string objectName, string filePath, string? bucketName = null, string? contentType = null, Dictionary<string, string>? metadata = null, CancellationToken cancellationToken = default) => Task.FromResult(new StorageUploadResult());
            public Task<StorageUploadResult> UploadLargeFileAsync(string objectName, string filePath, string? bucketName = null, string? contentType = null, Dictionary<string, string>? metadata = null, long partSize = 67108864, IProgress<StorageUploadProgress>? progressCallback = null, CancellationToken cancellationToken = default) => Task.FromResult(new StorageUploadResult());
            public Task DownloadObjectAsync(string objectName, Stream stream, string? bucketName = null, CancellationToken cancellationToken = default) => Task.CompletedTask;
            public Task DownloadFileAsync(string objectName, string filePath, string? bucketName = null, CancellationToken cancellationToken = default) => Task.CompletedTask;
            public Task<byte[]> GetObjectBytesAsync(string objectName, string? bucketName = null, CancellationToken cancellationToken = default) => Task.FromResult(Array.Empty<byte>());
            public Task<bool> ObjectExistsAsync(string objectName, string? bucketName = null, CancellationToken cancellationToken = default) => Task.FromResult(true);
            public Task DeleteObjectAsync(string objectName, string? bucketName = null, CancellationToken cancellationToken = default) => Task.CompletedTask;
            public Task<IEnumerable<StorageDeleteResult>> DeleteObjectsAsync(IEnumerable<string> objectNames, string? bucketName = null, CancellationToken cancellationToken = default) => Task.FromResult(Enumerable.Empty<StorageDeleteResult>());
            public Task<StorageObjectInfo> GetObjectInfoAsync(string objectName, string? bucketName = null, CancellationToken cancellationToken = default) => Task.FromResult(new StorageObjectInfo());
            public Task<IEnumerable<StorageObjectInfo>> ListObjectsAsync(string? prefix = null, bool recursive = true, string? bucketName = null, CancellationToken cancellationToken = default) => Task.FromResult(Enumerable.Empty<StorageObjectInfo>());
            public Task<string> GetPresignedDownloadUrlAsync(string objectName, TimeSpan expiryTimeSpan, string? bucketName = null, CancellationToken cancellationToken = default) => Task.FromResult("https://example.com/download");
            public Task<string> GetPresignedUploadUrlAsync(string objectName, TimeSpan expiryTimeSpan, string? bucketName = null, CancellationToken cancellationToken = default) => Task.FromResult("https://example.com/upload");
            public Task<string> GetPreviewUrlAsync(string objectName, string? bucketName = null, CancellationToken cancellationToken = default) => Task.FromResult("https://example.com/preview");
            public void Dispose() { }
        }
    }
}
