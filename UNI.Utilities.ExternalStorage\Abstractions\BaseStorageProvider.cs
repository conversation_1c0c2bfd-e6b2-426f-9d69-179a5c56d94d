using Microsoft.Extensions.Logging;
using UNI.Utilities.ExternalStorage.Models;

namespace UNI.Utilities.ExternalStorage.Abstractions
{
    /// <summary>
    /// Base abstract class for storage providers with common functionality
    /// </summary>
    /// <typeparam name="TSettings">Type of settings for this provider</typeparam>
    public abstract class BaseStorageProvider<TSettings> : IStorageService
        where TSettings : StorageSettings
    {
        protected readonly TSettings _settings;
        protected readonly ILogger _logger;
        private bool _disposed = false;

        /// <summary>
        /// Constructor for base storage provider
        /// </summary>
        /// <param name="settings">Provider-specific settings</param>
        /// <param name="logger">Logger instance</param>
        protected BaseStorageProvider(TSettings settings, ILogger logger)
        {
            _settings = settings ?? throw new ArgumentNullException(nameof(settings));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            
            ValidateSettings();
        }

        /// <summary>
        /// Storage provider type
        /// </summary>
        public abstract StorageProviderType ProviderType { get; }

        /// <summary>
        /// Storage provider name
        /// </summary>
        public abstract string ProviderName { get; }

        /// <summary>
        /// Validate provider-specific settings
        /// </summary>
        protected virtual void ValidateSettings()
        {
            if (_settings.TimeoutSeconds <= 0)
            {
                throw new ArgumentException("TimeoutSeconds must be greater than 0", nameof(_settings.TimeoutSeconds));
            }
        }

        /// <summary>
        /// Ensure bucket exists if CreateBucketIfNotExists is enabled
        /// </summary>
        /// <param name="bucketName">Name of the bucket (optional, uses default bucket if not provided)</param>
        /// <param name="cancellationToken">Cancellation token</param>
        protected virtual async Task EnsureBucketExistsAsync(string? bucketName = null, CancellationToken cancellationToken = default)
        {
            if (_settings.CreateBucketIfNotExists)
            {
                var resolvedBucketName = GetBucketName(bucketName);
                var bucketExists = await BucketExistsAsync(resolvedBucketName, cancellationToken);
                if (!bucketExists)
                {
                    _logger.LogInformation("Creating bucket {BucketName} as it doesn't exist", resolvedBucketName);
                    await CreateBucketAsync(resolvedBucketName, cancellationToken: cancellationToken);
                }
            }
        }

        /// <summary>
        /// Get the bucket name to use (from parameter or default)
        /// </summary>
        /// <param name="bucketName">Provided bucket name</param>
        /// <returns>Bucket name to use</returns>
        protected virtual string GetBucketName(string? bucketName = null)
        {
            if (!string.IsNullOrWhiteSpace(bucketName))
                return bucketName;

            if (!string.IsNullOrWhiteSpace(_settings.DefaultBucket))
                return _settings.DefaultBucket;

            throw new ArgumentException("Bucket name must be provided either as parameter or in settings.DefaultBucket");
        }

        /// <summary>
        /// Detect content type from file extension
        /// </summary>
        /// <param name="fileName">File name or object name</param>
        /// <returns>Content type</returns>
        protected virtual string GetContentType(string fileName)
        {
            var extension = Path.GetExtension(fileName).ToLowerInvariant();
            return extension switch
            {
                ".jpg" or ".jpeg" => "image/jpeg",
                ".png" => "image/png",
                ".gif" => "image/gif",
                ".bmp" => "image/bmp",
                ".webp" => "image/webp",
                ".svg" => "image/svg+xml",
                ".pdf" => "application/pdf",
                ".doc" => "application/msword",
                ".docx" => "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
                ".xls" => "application/vnd.ms-excel",
                ".xlsx" => "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                ".ppt" => "application/vnd.ms-powerpoint",
                ".pptx" => "application/vnd.openxmlformats-officedocument.presentationml.presentation",
                ".txt" => "text/plain",
                ".html" => "text/html",
                ".css" => "text/css",
                ".js" => "application/javascript",
                ".json" => "application/json",
                ".xml" => "application/xml",
                ".zip" => "application/zip",
                ".rar" => "application/x-rar-compressed",
                ".7z" => "application/x-7z-compressed",
                ".mp3" => "audio/mpeg",
                ".wav" => "audio/wav",
                ".mp4" => "video/mp4",
                ".avi" => "video/x-msvideo",
                ".mov" => "video/quicktime",
                _ => "application/octet-stream"
            };
        }

        #region Abstract Methods - Must be implemented by derived classes

        public abstract Task<bool> BucketExistsAsync(string? bucketName = null, CancellationToken cancellationToken = default);
        public abstract Task CreateBucketAsync(string? bucketName = null, string? region = null, CancellationToken cancellationToken = default);
        public abstract Task DeleteBucketAsync(string? bucketName = null, CancellationToken cancellationToken = default);
        public abstract Task<IEnumerable<BucketInfo>> ListBucketsAsync(CancellationToken cancellationToken = default);

        public abstract Task<StorageUploadResult> UploadObjectAsync(string objectName, Stream stream, string? bucketName = null, long? size = null, string? contentType = null, Dictionary<string, string>? metadata = null, CancellationToken cancellationToken = default);
        public abstract Task<StorageUploadResult> UploadFileAsync(string objectName, string filePath, string? bucketName = null, string? contentType = null, Dictionary<string, string>? metadata = null, CancellationToken cancellationToken = default);
        public abstract Task<StorageUploadResult> UploadLargeFileAsync(string objectName, string filePath, string? bucketName = null, string? contentType = null, Dictionary<string, string>? metadata = null, long partSize = 67108864, IProgress<StorageUploadProgress>? progressCallback = null, CancellationToken cancellationToken = default);

        public abstract Task DownloadObjectAsync(string objectName, Stream stream, string? bucketName = null, CancellationToken cancellationToken = default);
        public abstract Task DownloadFileAsync(string objectName, string filePath, string? bucketName = null, CancellationToken cancellationToken = default);
        public abstract Task<byte[]> GetObjectBytesAsync(string objectName, string? bucketName = null, CancellationToken cancellationToken = default);

        public abstract Task<bool> ObjectExistsAsync(string objectName, string? bucketName = null, CancellationToken cancellationToken = default);
        public abstract Task DeleteObjectAsync(string objectName, string? bucketName = null, CancellationToken cancellationToken = default);
        public abstract Task<IEnumerable<StorageDeleteResult>> DeleteObjectsAsync(IEnumerable<string> objectNames, string? bucketName = null, CancellationToken cancellationToken = default);

        public abstract Task<StorageObjectInfo> GetObjectInfoAsync(string objectName, string? bucketName = null, CancellationToken cancellationToken = default);
        public abstract Task<IEnumerable<StorageObjectInfo>> ListObjectsAsync(string? prefix = null, bool recursive = true, string? bucketName = null, CancellationToken cancellationToken = default);

        public abstract Task<string> GetPresignedDownloadUrlAsync(string objectName, TimeSpan expiryTimeSpan, string? bucketName = null, CancellationToken cancellationToken = default);
        public abstract Task<string> GetPresignedUploadUrlAsync(string objectName, TimeSpan expiryTimeSpan, string? bucketName = null, CancellationToken cancellationToken = default);
        public abstract Task<string> GetPreviewUrlAsync(string objectName, string? bucketName = null, CancellationToken cancellationToken = default);

        #endregion

        #region IDisposable Implementation

        /// <summary>
        /// Dispose the storage provider
        /// </summary>
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        /// <summary>
        /// Dispose the storage provider
        /// </summary>
        /// <param name="disposing">Whether disposing</param>
        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed && disposing)
            {
                // Dispose managed resources
                DisposeCore();
                _disposed = true;
            }
        }

        /// <summary>
        /// Override this method to dispose provider-specific resources
        /// </summary>
        protected virtual void DisposeCore()
        {
            // Override in derived classes to dispose provider-specific resources
        }

        #endregion
    }
}
